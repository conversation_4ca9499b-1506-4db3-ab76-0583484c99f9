import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown } from 'lucide-react';

interface SelectProps {
  children: React.ReactNode;
  value?: string;
  onValueChange?: (value: string) => void;
  className?: string;
}

interface SelectTriggerProps {
  children: React.ReactNode;
  className?: string;
}

interface SelectContentProps {
  children: React.ReactNode;
  align?: 'start' | 'center' | 'end';
}

interface SelectItemProps {
  children: React.ReactNode;
  value: string;
  className?: string;
}

interface SelectValueProps {
  children?: React.ReactNode;
}

export function Select({ children, value, onValueChange, className = '' }: SelectProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedValue, setSelectedValue] = useState(value);
  const triggerRef = useRef<HTMLButtonElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setSelectedValue(value);
  }, [value]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        triggerRef.current &&
        !triggerRef.current.contains(event.target as Node) &&
        contentRef.current &&
        !contentRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSelect = (newValue: string) => {
    setSelectedValue(newValue);
    onValueChange?.(newValue);
    setIsOpen(false);
  };

  return (
    <div className={`relative ${className}`}>
      {React.Children.map(children, (child) => {
        if (React.isValidElement(child)) {
          if (child.type === SelectTrigger) {
            return React.cloneElement(child as any, {
              ref: triggerRef as any,
              onClick: () => setIsOpen(!isOpen),
              isOpen,
              selectedValue
            });
          }
          if (child.type === SelectContent && isOpen) {
            return React.cloneElement(child as any, {
              ref: contentRef as any,
              onSelect: handleSelect
            });
          }
        }
        return child;
      })}
    </div>
  );
}

export const SelectTrigger = React.forwardRef<HTMLButtonElement, SelectTriggerProps & { onClick?: () => void; isOpen?: boolean; selectedValue?: string }>(
  ({ children, className = '', onClick, isOpen, selectedValue }, ref) => {
    return (
      <button
        ref={ref}
        onClick={onClick}
        className={`flex h-10 w-full items-center justify-between rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-800 dark:ring-offset-gray-950 dark:placeholder:text-gray-400 dark:focus:ring-gray-300 ${className}`}
      >
        {selectedValue || children}
        <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>
    );
  }
);

export const SelectContent = React.forwardRef<HTMLDivElement, SelectContentProps & { onSelect?: (value: string) => void }>(
  ({ children, align = 'start', onSelect }, ref) => {
    return (
      <div
        ref={ref}
        className={`absolute z-50 min-w-[8rem] overflow-hidden rounded-md border border-gray-200 bg-white p-1 text-gray-950 shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 dark:border-gray-800 dark:bg-gray-950 dark:text-gray-50`}
        style={{
          top: '100%',
          left: align === 'start' ? '0' : align === 'center' ? '50%' : 'auto',
          right: align === 'end' ? '0' : 'auto',
          transform: align === 'center' ? 'translateX(-50%)' : 'none'
        }}
      >
        {React.Children.map(children, (child) => {
          if (React.isValidElement(child) && child.type === SelectItem) {
            return React.cloneElement(child as any, {
              onSelect: onSelect
            });
          }
          return child;
        })}
      </div>
    );
  }
);

export const SelectItem = React.forwardRef<HTMLDivElement, SelectItemProps & { onSelect?: (value: string) => void }>(
  ({ children, value, className = '', onSelect }, ref) => {
    return (
      <div
        ref={ref}
        className={`relative flex w-full cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-gray-100 focus:bg-gray-100 focus:text-gray-900 data-[disabled]:pointer-events-none data-[disabled]:opacity-50 dark:hover:bg-gray-800 dark:focus:bg-gray-800 dark:focus:text-gray-50 ${className}`}
        onClick={() => onSelect?.(value)}
      >
        {children}
      </div>
    );
  }
);

export function SelectValue({ children }: SelectValueProps) {
  return <span>{children}</span>;
} 