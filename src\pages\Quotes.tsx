import React, { useState, useEffect, useRef } from 'react';
import { Calculator, Trash2, Plus, Save, RotateCcw, Download, ChevronDown, ChevronUp, Mail, Users, X, MapPin, Search } from 'lucide-react';
import { HotelAutocomplete } from '../quotes/components/HotelAutocomplete';
// import { PackageAutocomplete } from '../quotes/components/PackageAutocomplete';
import { DestinationAutocomplete } from '../quotes/components/DestinationAutocomplete';
import { FamilyTypeAutocomplete } from '../quotes/components/FamilyTypeAutocomplete';
import { PackageTypeAutocomplete } from '../quotes/components/PackageTypeAutocomplete';
import { RoomTypeAutocomplete } from '../quotes/components/RoomTypeAutocomplete';
import FamilyTypeTab from '../quotes/Tabs/Familytype';
import QuoteMappingTab from '../quotes/Tabs/QuoteMapping';
// import jsPDF from 'jspdf';
// import { autoTable as originalAutoTable } from 'jspdf-autotable';

import emailjs from '@emailjs/browser';
// import axios from 'axios';
import { formatPrice, CURRENCIES, setActiveCurrency, convertCurrency } from '../quotes/utils/formatters';
import { generateFamilyTypePrices, FamilyTypePriceResult, QuoteGeneratorData } from '../quotes/utils/familyTypeQuoteGenerator';
import EmiCalculator from '../quotes/Tabs/EmiCalculator';
// import { v4 as uuidv4 } from 'uuid';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate, Link } from 'react-router-dom';
import { getQuoteClient } from '../lib/supabaseManager';
import { syncQuoteWithLead, updateLeadFromQuoteAction } from '../lib/quoteLead Integration';
// import { fetchLeadById } from '../lib/leadService';
// import { supabase } from '../lib/supabaseClient';
// import CurrencySelector from '../quotes/components/CurrencySelector';

const EMAIL_CONFIG = {
  SERVICE_ID: 'service_f2y5i5p',
  TEMPLATE_ID: 'template_26ri3ai',
  PUBLIC_KEY: 'OOY-mqlCSb0wCaK4k',
  USER_ID: 'OOY-mqlCSb0wCaK4k'
};

export const CAB_TYPE_OPTIONS = [
  "Dzire / Etios / Similar (Sedan)",
  "Suzuki Ertiga / Similar",
  "Toyota Innova",
  "Toyota Innova Crysta",
  "Tempo Traveller 8 seater",
  "Tempo Traveller 10 seater",
  "Tempo Traveller 12 seater",
  "Tempo Traveller 10-12 seater",
  "Premium - URBANIA - 15 seater",
  "Tempo Traveller 16 seater"
] as const;

interface HotelRow {
  hotelName: string;
  roomType: string;
  roomCapacity: number; // New field for room capacity
  price: number;
  mealPlan: typeof MEAL_PLAN_OPTIONS[number];
  noOfRooms: number;
  stayNights: number;
  extraAdultCost: number;
  childrenCost: number;
  infantCost: number;
  gstType: '0' | '12' | '18' | 'NET' | 'EXC';
  tacPercentage: number;
  tacAmount: number;
  info: string;
  stayPrice: number;
  gstAmount: number;
  currency?: string; // Add currency property
}

interface PriceCalculationResult {
  basePrice: number;
  extraCosts: number;
  subtotal: number;
  gstAmount: number;
  tacAmount: number;
  finalPrice: number;
  showGstSeparately: boolean;
  currency?: string;
}

const NumberInput = ({
  value,
  onChange,
  min = 0,
  max,
  className = "",
  isPrice = false
}: {
  value: number;
  onChange: (value: number) => void;
  min?: number;
  max?: number;
  className?: string;
  isPrice?: boolean;
}) => {
  const [displayValue, setDisplayValue] = useState(value.toString());

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let inputValue = e.target.value;

    // Remove leading zeros and any non-digit characters except decimal point
    inputValue = inputValue.replace(/^0+(?=\d)/, '');

    // Update display value
    setDisplayValue(inputValue);

    // Convert to number and update parent
    const numberValue = inputValue === '' ? 0 : Number(inputValue);
    if (!isNaN(numberValue) && numberValue >= min) {
      onChange(numberValue);
    }
  };

  // Keep the display value synchronized with external value changes
  useEffect(() => {
    if (value === 0 && displayValue !== '0' && displayValue !== '') {
      setDisplayValue('0');
    } else if (value !== Number(displayValue)) {
      setDisplayValue(value.toString());
    }
  }, [value]);

  return (
    <input
      type="number"
      value={displayValue}
      onChange={handleChange}
      onFocus={(_e) => displayValue === '0' && setDisplayValue('')}
      onBlur={(e) => e.target.value === '' && setDisplayValue('0')}
      className={`w-full px-2 py-1.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#00B69B] focus:border-[#00B69B] ${className}`}
      min={min}
      max={max}
      step={isPrice ? 100 : 1}
    />
  );
};

const GST_OPTIONS = [
  { value: '0', label: '0%' },
  { value: '12', label: '12%' },
  { value: '18', label: '18%' },
  { value: 'NET', label: 'NET Price' },
  { value: 'EXC', label: 'Exc GST' }
];

const TAC_OPTIONS = [
  { value: 0, label: '0%' },
  { value: 5, label: '5%' },
  { value: 10, label: '10%' },
  { value: 15, label: '15%' }
];

const MEAL_PLAN_OPTIONS = [
  'EP',
  'CP',
  'MAP',
  'CP - Weekday',
  'CP - Weekend',
  'EP - Weekend',
  'EP - Weekday',
  'CP - Single',
  'MAP - Weekend',
  'MAP - Weekday',
  'MAP - Single',
  'Extra Bed',
  'Extra Bed - CP',
  'Extra Bed - MAP',
  'Extra Child',
  'Extra Child - CP',
  'Extra Child - MAP',
  'AP'
] as const;

const COMMISSION_OPTIONS = [
  { value: 5, label: '5%' },
  { value: 8, label: '8%' },
  { value: 10, label: '10%' },
  { value: 12, label: '12%' },
  { value: 15, label: '15%' },
  { value: 18, label: '18%' },
  { value: 20, label: '20%' },
  { value: 22, label: '22%' },
  { value: 25, label: '25%' },
  { value: 'custom', label: 'Custom' }
] as const;

type CommissionValue = typeof COMMISSION_OPTIONS[number]['value'];

interface CostGroups {
  basicCosts: {
    meals: number;
    transportation: number;
    cabSightseeing: number;
    trainCost: number;
    ferryCost: number;
    parkingToll: number;
  };
  addOnCosts: {
    addOnActivity: number;
    marketing: number;
    addOn: number;
  };
  optionalCosts: {
    flightTicket: number;
    guideWages: number;
  };
}

emailjs.init("YujCzV0dd0QBkAl30");

type TabType = 'general' | 'family' | 'quote-mapping' | 'emi' | 'packageOptions';

interface Option {
  value: string;
  label: string;
}

interface ChecklistItem {
  id: string;
  text: string;
  isSelected: boolean;
}

interface GeneralOptions {
  plans: Option[];
  seasons: Option[];
  quoteTypes: Option[];
  durations: string[];
  destinations: string[];
  packageTypes: Option[];
}

const GENERAL_OPTIONS: GeneralOptions = {
  plans: [
    { value: 'Silver', label: 'Silver Package' },
    { value: 'Gold', label: 'Gold Package' },
    { value: 'Platinum', label: 'Platinum Package' },
    { value: 'Diamond', label: 'Diamond Package' }
  ],
  seasons: [
    { value: "Normal", label: "Normal" },
    { value: "Peak Season", label: "Peak Season" },
    { value: "Offer", label: "Offer" },
    { value: "ON-Season", label: "ON-Season" },
    { value: "OFF-Season", label: "OFF-Season" },
    { value: "Discount", label: "Discount" },
    { value: "Mid Season", label: "Mid Season" },
    { value: "Festive season", label: "Festive season" },
    { value: "Future", label: "Future" },
    { value: "Block Out", label: "Block Out" },
    { value: "Weekend Break", label: "Weekend Break" },
    { value: "Long Weekends", label: "Long Weekends" },
    { value: "National Holidays", label: "National Holidays" },
    { value: "New Year", label: "New Year" },
    { value: "Diwali", label: "Diwali" },
    { value: "Christmas", label: "Christmas" },
    { value: "Summer", label: "Summer" }
  ],
  quoteTypes: [
    { value: 'Tentative', label: 'Tentative' },
    { value: 'Add', label: 'Add Package' },
    { value: 'Customer', label: 'Customer Quote' },
    { value: 'Custom', label: 'NIL' }
  ],
  durations: [
    '2N/3D',
    '3N/4D',
    '4N/5D',
    '5N/6D',
    '6N/7',
    '7N/8D',
    '8N/9D',
    '9N/10D',
    '10N/11D'
  ],
  destinations: [
    'Kerala - Gods Own Country',
    'Goa - Beach Paradise',
    'Rajasthan - Royal Heritage',
    'Himachal - Mountain Paradise',
    'Karnataka - Cultural Haven',
    'Andaman - Island Paradise',
    'Sikkim - Northeast Wonder',
    'Kashmir - Heaven on Earth'
  ],
  packageTypes: [
    { value: 'Solo', label: 'Solo' },
    { value: 'Couples', label: 'Couples' },
    { value: 'Honeymoon', label: 'Honeymoon' },
    { value: 'Family', label: 'Family' },
    { value: 'Group', label: 'Group' },
    { value: 'Worship-Religious', label: 'Worship - Religious' },
    { value: 'Pocket-Friendly', label: 'Pocket Friendly' },
    { value: 'Silver', label: 'Silver' },
    { value: 'Gold', label: 'Gold' },
    { value: 'Platinum', label: 'Platinum' },
    { value: 'Diamond', label: 'Diamond' }
  ]
};


// Predefined inclusion options
const DEFAULT_INCLUSIONS = [
  { id: "inc1", text: "Welcome Drink on Arrival", isSelected: true },
  { id: "inc2", text: "Daily Breakfast Included", isSelected: false },
  { id: "inc3", text: "Daily Breakfast & Lunch Included", isSelected: true },
  { id: "inc4", text: "Private Cab for Transfer + Sightseeing", isSelected: true },
  { id: "inc5", text: "Free ICICI Lombard - 10 Days Domestic Travel Insurance", isSelected: false },
  { id: "inc6", text: "All Taxes Included", isSelected: false },
  { id: "inc7", text: "24/7 On-Trip Support", isSelected: true },
  { id: "inc8", text: "Complimentary Room Upgrade (Subject to Availability)", isSelected: false },
  { id: "inc9", text: "Airport Transfers", isSelected: false },
  { id: "inc10", text: "Guided Sightseeing Tours", isSelected: false },
  { id: "inc11", text: "Entry Tickets to Monuments", isSelected: false }
];

// Predefined exclusion options
const DEFAULT_EXCLUSIONS = [
  { id: "exc1", text: "Train or Airfare", isSelected: true },
  { id: "exc2", text: "Personal expenses (telephone, internet, laundry, etc.)", isSelected: true },
  { id: "exc3", text: "Adventure activities unless specified", isSelected: true },
  { id: "exc4", text: "Additional sightseeing", isSelected: true },
  { id: "exc5", text: "Museum/park entry fees", isSelected: false },
  { id: "exc6", text: "Jeep safari charges", isSelected: false },
  { id: "exc7", text: "Room heater charges", isSelected: false },
  { id: "exc8", text: "Snow vehicle fare", isSelected: false },
  { id: "exc9", text: "Travel insurance unless specified", isSelected: false },
  { id: "exc10", text: "Anything not mentioned in inclusion", isSelected: false }
];

// Default content for the PDF
const DEFAULT_PAYMENT_OPTIONS: ChecklistItem[] = [
  { id: 'po1', text: "• 50% advance payment at the time of booking", isSelected: true },
  { id: 'po2', text: "• Balance 50% payment 15 days before travel", isSelected: true },
  { id: 'po3', text: "• Payment can be made via bank transfer, UPI, or credit card", isSelected: true }
];

// const DEFAULT_PAYMENT_POLICIES = [
//   ["Time Before Trip", "Payment Required"],
//   ["At the time of booking", "50% of package cost"],
//   ["15 days before trip", "Remaining 50% of package cost"]
// ];

// const DEFAULT_REFUND_POLICIES = [
//   ["More than 30 days before trip", "Full Refund"],
//   ["15-30 days before trip", "50% Refund"],
//   ["Within 15 days before trip", "No Refund"]
// ];

const DEFAULT_TERMS: ChecklistItem[] = [
  { id: 't1', text: "• Prices are subject to change without prior notice", isSelected: true },
  { id: 't2', text: "• Hotel check-in/check-out times are as per hotel policy", isSelected: true },
  { id: 't3', text: "• Rates are valid for the mentioned dates only", isSelected: true },
  { id: 't4', text: "• All disputes are subject to Chennai jurisdiction", isSelected: false }
];

const ChecklistSelector = ({
  items,
  onChange,
  title,
  allowAddNew = true
}: {
  items: ChecklistItem[];
  onChange: (items: ChecklistItem[]) => void;
  title: string;
  allowAddNew?: boolean;
}) => {
  const [newItemText, setNewItemText] = useState("");
  const [editingItemId, setEditingItemId] = useState<string | null>(null);
  const [editingItemText, setEditingItemText] = useState("");

  const handleToggle = (id: string) => {
    const updatedItems = items.map(item =>
      item.id === id ? { ...item, isSelected: !item.isSelected } : item
    );
    onChange(updatedItems);
  };

  const handleAddItem = () => {
    if (!newItemText.trim()) return;

    const newItem: ChecklistItem = {
      id: `custom-${Date.now()}`,
      text: newItemText.trim(),
      isSelected: true
    };

    onChange([...items, newItem]);
    setNewItemText("");
  };

  const handleEditItem = (id: string) => {
    const item = items.find(item => item.id === id);
    if (item) {
      setEditingItemId(id);
      setEditingItemText(item.text);
    }
  };

  const handleSaveEdit = () => {
    if (!editingItemId || !editingItemText.trim()) {
      setEditingItemId(null);
      return;
    }

    const updatedItems = items.map(item =>
      item.id === editingItemId ? { ...item, text: editingItemText.trim() } : item
    );
    onChange(updatedItems);
    setEditingItemId(null);
  };

  const handleDeleteItem = (id: string) => {
    const updatedItems = items.filter(item => item.id !== id);
    onChange(updatedItems);
  };

  return (
    <div className="mb-4">
      <label className="block text-sm font-medium text-gray-700 mb-2">{title}</label>
      <div className="border border-gray-300 rounded-md p-2 bg-white">
        <div className="max-h-40 overflow-y-auto mb-2">
          {items.map(item => (
            <div key={item.id} className="flex items-center py-1 group">
              <input
                type="checkbox"
                id={item.id}
                checked={item.isSelected}
                onChange={() => handleToggle(item.id)}
                className="mr-2 h-4 w-4 text-primary focus:ring-primaryDark rounded"
              />

              {editingItemId === item.id ? (
                <div className="flex flex-1">
                  <input
                    type="text"
                    value={editingItemText}
                    onChange={(e) => setEditingItemText(e.target.value)}
                    className="flex-1 p-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primaryDark"
                    onKeyPress={(e) => e.key === 'Enter' && handleSaveEdit()}
                  />
                  <button
                    type="button"
                    onClick={handleSaveEdit}
                    className="ml-2 text-primary hover:text-primaryDark"
                  >
                    Save
                  </button>
                </div>
              ) : (
                <>
                  <label htmlFor={item.id} className="text-sm text-gray-700 flex-1">{item.text}</label>
                  <div className="hidden group-hover:flex items-center ml-2">
                    <button
                      type="button"
                      onClick={() => handleEditItem(item.id)}
                      className="text-gray-500 hover:text-primary mr-2"
                    >
                      Edit
                    </button>
                    <button
                      type="button"
                      onClick={() => handleDeleteItem(item.id)}
                      className="text-gray-500 hover:text-red-500"
                    >
                      Delete
                    </button>
                  </div>
                </>
              )}
            </div>
          ))}
        </div>

        {allowAddNew && (
          <div className="flex mt-2">
            <input
              type="text"
              value={newItemText}
              onChange={(e) => setNewItemText(e.target.value)}
              placeholder="Add custom item..."
              className="flex-1 p-2 text-sm border border-gray-300 rounded-l-md focus:outline-none focus:ring-1 focus:ring-primaryDark"
              onKeyPress={(e) => e.key === 'Enter' && handleAddItem()}
            />
            <button
              type="button"
              onClick={handleAddItem}
              className="bg-primary text-white px-3 rounded-r-md hover:bg-primaryDark transition-colors"
            >
              Add
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

// Define promo code interface
interface PromoCode {
  code: string;
  description: string;
  type: 'percentage' | 'amount';
  value: number;
  validUntil?: string; // Optional expiry date
}

// Add initial promo codes
const DEFAULT_PROMO_CODES: PromoCode[] = [
  {
    code: 'FIRSTXPLO',
    description: 'First time TripXplo user',
    type: 'percentage',
    value: 10,
    validUntil: '2023-12-31'
  },
  {
    code: 'SUMMER2023',
    description: 'Summer vacation special',
    type: 'amount',
    value: 1000,
    validUntil: '2023-08-31'
  }
];

// Using the supabase client imported from '../lib/supabaseClient'

function App() {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  // State to hold the Supabase client
  const [supabase, setSupabase] = useState<any>(null);
  const [isClientLoading, setIsClientLoading] = useState(true);

  // Initialize the Supabase client
  useEffect(() => {
    const initSupabase = async () => {
      try {
        setIsClientLoading(true);
        const client = await getQuoteClient();
        setSupabase(client);
      } catch (error) {
        console.error('Error initializing Quote Supabase client:', error);
      } finally {
        setIsClientLoading(false);
      }
    };

    initSupabase();
  }, []);

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Error during sign out:', error);
    }
  };

  // --- Recent Quotes Dropdown State ---
  const [showRecentQuotesDropdown, setShowRecentQuotesDropdown] = useState(false);
  const [recentQuotes, setRecentQuotes] = useState<any[]>([]);
  const recentQuotesDropdownRef = useRef<HTMLDivElement>(null);

  // Fetch last 10 quotes when dropdown is opened
  useEffect(() => {
    if (showRecentQuotesDropdown && supabase) {
      (async () => {
        try {
          const { data, error } = await supabase
            .from('quotes')
            .select('*, total_cost')
            .order('created_at', { ascending: false })
            .limit(10);
          if (!error && data) setRecentQuotes(data);
        } catch (err) {
          console.error('Error fetching recent quotes:', err);
          // Continue with empty quotes array
          setRecentQuotes([]);
        }
      })();
    }
  }, [showRecentQuotesDropdown, supabase]);

  // Close dropdown on click outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        recentQuotesDropdownRef.current &&
        !recentQuotesDropdownRef.current.contains(event.target as Node)
      ) {
        setShowRecentQuotesDropdown(false);
      }
    }
    if (showRecentQuotesDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showRecentQuotesDropdown]);
  // Load initial state from localStorage or use defaults
  const loadInitialState = () => {
    const savedState = localStorage.getItem('tripXploState');
    if (savedState) {
      return JSON.parse(savedState);
    }
    return {
      packageId: 'PKG-' + Math.random().toString(36).substr(2, 9),
      packageName: '',
      customerName: '',
      plan: 'Silver',
      destination: '',
      noOfPersons: 0,
      extraAdults: 0,
      children: 0,
      infants: 0,
      quoteDate: new Date().toISOString().split('T')[0],
      validityDate: '',
      season: '',
      cabType: '',
      quoteType: '',
      tripDuration: '',
      familyType: '',
      packageType: '',
      hotelRows: [{
    hotelName: '',
    roomType: 'Standard',
    roomCapacity: 2,
    price: 0,
    mealPlan: 'MAP',
    noOfRooms: 0,
    stayNights: 0,
    extraAdultCost: 0,
    childrenCost: 0,
    infantCost: 0,
    gstType: '0',
    tacPercentage: 0,
    tacAmount: 0,
    info: '',
    stayPrice: 0,
    gstAmount: 0
      }],
      costs: {
    basicCosts: {
      meals: 0,
      transportation: 0,
      cabSightseeing: 0,
      trainCost: 0,
      ferryCost: 0,
      parkingToll: 0,
    },
    addOnCosts: {
      addOnActivity: 0,
      marketing: 0,
      addOn: 0,
    },
    optionalCosts: {
      flightTicket: 0,
      guideWages: 0,
    }
      },
      commission: 5,
      customCommission: 0,
      isStaySummaryOpen: true,
      recipientEmail: '',
      recipientPhoneNumber: '',
      activeTab: 'general',
      isEmailSending: false,
      isWhatsAppSending: false,
      isPdfGenerating: false,
      currency: 'INR', // Default currency
      discountType: 'percentage',
      discountValue: 0,
      discountMode: 'manual',
      selectedPromoCode: '',
      promoCodes: DEFAULT_PROMO_CODES,
      inclusionItems: DEFAULT_INCLUSIONS,
      exclusionItems: DEFAULT_EXCLUSIONS,
      paymentOptionItems: DEFAULT_PAYMENT_OPTIONS,
      termItems: DEFAULT_TERMS,
    };
  };

  // Initialize state with saved data
  const initialState = loadInitialState();
  const initialCostsState = initialState.costs;
  const [packageId, setPackageId] = useState(initialState.packageId);
  const [packageName, setPackageName] = useState(initialState.packageName);
  const [customerName, setCustomerName] = useState(initialState.customerName);
  const [plan, setPlan] = useState(initialState.plan);
  const [destination, setDestination] = useState(initialState.destination);
  const [noOfPersons, setNoOfPersons] = useState(initialState.noOfPersons);
  const [extraAdults, setExtraAdults] = useState(initialState.extraAdults);
  const [children, setChildren] = useState(initialState.children);
  const [infants, setInfants] = useState(initialState.infants);
  const [quoteDate, setQuoteDate] = useState(initialState.quoteDate);
  const [validityDate, setValidityDate] = useState(initialState.validityDate);
  const [season, setSeason] = useState(initialState.season);
  const [cabType, setCabType] = useState(initialState.cabType);
  // const [cabSeating, setCabSeating] = useState('');
  const [quoteType, setQuoteType] = useState(initialState.quoteType);
  const [tripDuration, setTripDuration] = useState(initialState.tripDuration);
  const [tripType, setTripType] = useState(initialState.familyType || '');
  const [packageType, setPackageType] = useState(initialState.packageType || '');
  const [hotelRows, setHotelRows] = useState<HotelRow[]>(initialState.hotelRows);
  const [costs, setCosts] = useState<CostGroups>(initialState.costs);
  const [commission, setCommission] = useState<CommissionValue>(initialState.commission);
  const [customCommission, setCustomCommission] = useState(initialState.customCommission);
  const [isStaySummaryOpen, setIsStaySummaryOpen] = useState(initialState.isStaySummaryOpen);
  const [recipientEmail, setRecipientEmail] = useState(initialState.recipientEmail);
  const [recipientPhoneNumber, setRecipientPhoneNumber] = useState(initialState.recipientPhoneNumber);
  const [activeTab, setActiveTab] = useState<TabType>(initialState.activeTab);
  const [activeMainTab, setActiveMainTab] = useState<'new-quote' | 'saved-quotes' | 'settings' | 'help'>('new-quote');
  const [isEmailSending, setIsEmailSending] = useState(initialState.isEmailSending);
  const [isWhatsAppSending, setIsWhatsAppSending] = useState(initialState.isWhatsAppSending);
  const [isPdfGenerating, _setIsPdfGenerating] = useState(initialState.isPdfGenerating);
  const [selectedCurrency, setSelectedCurrency] = useState(initialState.currency || 'INR');
  const [isFamilyTypePricesGenerating, setIsFamilyTypePricesGenerating] = useState(false);
  const [familyTypePricesResults, setFamilyTypePricesResults] = useState<FamilyTypePriceResult[]>([]);
  const [familyTypePricesMessage, setFamilyTypePricesMessage] = useState('');
  const [discountType, setDiscountType] = useState<'percentage' | 'amount'>('percentage');
  const [discountValue, setDiscountValue] = useState<number>(0);
  const [selectedPromoCode, setSelectedPromoCode] = useState<string>('');
  const [discountMode, setDiscountMode] = useState<'manual' | 'promo'>('manual');
  const [promoCodes, setPromoCodes] = useState<PromoCode[]>(initialState.promoCodes || DEFAULT_PROMO_CODES);
  const [newPromoCode, setNewPromoCode] = useState<{
    code: string;
    description: string;
    type: 'percentage' | 'amount';
    value: number;
    validUntil: string;
  }>({
    code: '',
    description: '',
    type: 'percentage',
    value: 0,
    validUntil: ''
  });

  // Update active currency when selected currency changes
  useEffect(() => {
    setActiveCurrency(selectedCurrency);
  }, [selectedCurrency]);

  // Auto-load quote if selected from Customer Management
  useEffect(() => {
    const selectedQuoteId = sessionStorage.getItem('selectedQuoteId');
    if (selectedQuoteId) {
      console.log('Loading quote from Customer Management:', selectedQuoteId);
      loadQuote(selectedQuoteId);
      sessionStorage.removeItem('selectedQuoteId'); // Clear after loading
    }
  }, [supabase]);

  // Auto-fill form from lead data if available
  useEffect(() => {
    const leadDataString = sessionStorage.getItem('leadDataForQuote');
    if (leadDataString) {
      try {
        const leadData = JSON.parse(leadDataString);
        console.log('Auto-filling quote form from lead data:', leadData);

        // Auto-fill form fields with lead data
        if (leadData.customerName) {
          setCustomerName(leadData.customerName);
        }
        if (leadData.destination) {
          setDestination(leadData.destination);
        }
        if (leadData.travelDate) {
          // Convert travel date to quote date format if needed
          setQuoteDate(leadData.travelDate);
        }
        if (leadData.email) {
          setRecipientEmail(leadData.email);
        }
        if (leadData.phone) {
          setRecipientPhoneNumber(leadData.phone);
        }

        // Auto-fill travel group information
        if (leadData.adults && leadData.adults > 0) {
          setNoOfPersons(leadData.adults);
        }
        if (leadData.children && leadData.children >= 0) {
          setChildren(leadData.children);
        }
        if (leadData.infants && leadData.infants >= 0) {
          setInfants(leadData.infants);
        }

        // Auto-fill trip duration based on nights
        if (leadData.nights && leadData.nights > 0) {
          const duration = `${leadData.nights}N/${leadData.nights + 1}D`;
          setTripDuration(duration);
        }

        // Clear the stored data after using it
        sessionStorage.removeItem('leadDataForQuote');

        // Show a notification that the form was auto-filled
        console.log('Quote form auto-filled from lead data');

        // Show a brief notification to the user
        setTimeout(() => {
          alert(`Quote form auto-filled with data from lead: ${leadData.customerName}`);
        }, 500); // Small delay to ensure the form is rendered

      } catch (error) {
        console.error('Error parsing lead data from sessionStorage:', error);
        // Clear invalid data
        sessionStorage.removeItem('leadDataForQuote');
      }
    }
  }, []); // Run only once on component mount

  // Add state for inclusions and exclusions
  const [inclusionItems, setInclusionItems] = useState<ChecklistItem[]>(initialState.inclusionItems || DEFAULT_INCLUSIONS);
  const [exclusionItems, setExclusionItems] = useState<ChecklistItem[]>(initialState.exclusionItems || DEFAULT_EXCLUSIONS);
  const [paymentOptionItems, setPaymentOptionItems] = useState<ChecklistItem[]>(initialState.paymentOptionItems || DEFAULT_PAYMENT_OPTIONS);
  const [termItems, setTermItems] = useState<ChecklistItem[]>(initialState.termItems || DEFAULT_TERMS);

  // const [startDate, setStartDate] = useState<Date | null>(null);
  // const [dueDate, setDueDate] = useState<Date | null>(null);
  // const [adults, setAdults] = useState(2);
  // const [kids, setKids] = useState(0);

  // Save state to localStorage whenever it changes
  useEffect(() => {
    const stateToSave = {
      packageId,
      packageName,
      customerName,
      plan,
      destination,
      noOfPersons,
      extraAdults,
      children,
      infants,
      quoteDate,
      validityDate,
      season,
      cabType,
      quoteType,
      tripDuration,
      familyType: tripType,
      packageType,
      hotelRows,
      costs,
      commission,
      customCommission,
      isStaySummaryOpen,
      recipientEmail,
      recipientPhoneNumber,
      activeTab,
      isEmailSending,
      isWhatsAppSending,
      isPdfGenerating,
      inclusionItems,
      exclusionItems,
      paymentOptionItems,
      termItems,
      currency: selectedCurrency, // Add currency to saved state
      discountType,
      discountValue,
      discountMode,
      selectedPromoCode,
      promoCodes,
    };
    localStorage.setItem('tripXploState', JSON.stringify(stateToSave));
  }, [
    packageId,
    packageName,
    customerName,
    plan,
    destination,
    noOfPersons,
    extraAdults,
    children,
    infants,
    quoteDate,
    validityDate,
    season,
    cabType,
    quoteType,
    tripDuration,
    tripType,
    packageType,
    hotelRows,
    costs,
    commission,
    customCommission,
    isStaySummaryOpen,
    recipientEmail,
    recipientPhoneNumber,
    activeTab,
    isEmailSending,
    isWhatsAppSending,
    isPdfGenerating,
    inclusionItems,
    exclusionItems,
    paymentOptionItems,
    termItems,
    selectedCurrency, // Add to dependency array
    discountType,
    discountValue,
    discountMode,
    selectedPromoCode,
    promoCodes,
  ]);

  // Add useEffect to recalculate prices when extraAdults or children change
  useEffect(() => {
    const newRows = hotelRows.map(row => {
      if (row.noOfRooms > 0 && row.stayNights > 0) {
        const priceCalc = calculateHotelStayPrice(row, extraAdults, children);
      return {
          ...row,
          tacAmount: priceCalc.tacAmount,
          gstAmount: priceCalc.gstAmount,
          stayPrice: priceCalc.finalPrice
        };
      }
      return row;
    });
    setHotelRows(newRows);
  }, [extraAdults, children]);

  // Modified to handle currency
  const calculateHotelStayPrice = (row: HotelRow, extraAdultsCount: number, childrenCount: number): PriceCalculationResult => {
    // Basic price calculation
    const roomNightPrice = row.price * row.noOfRooms * row.stayNights;

    // Extra adults cost
    const extraAdultTotal = row.extraAdultCost * extraAdultsCount * row.stayNights;

    // Children cost
    const childrenTotal = row.childrenCost * childrenCount * row.stayNights;

    // Base price is room nights plus extras
    const basePrice = roomNightPrice;
    const extraCosts = extraAdultTotal + childrenTotal;

    // Subtotal before GST and TAC
    const subtotal = basePrice + extraCosts;

    // GST calculation based on type
    let gstAmount = 0;
    if (row.gstType !== 'NET' && row.gstType !== 'EXC') {
      gstAmount = (subtotal * Number(row.gstType)) / 100;
    }

    // TAC calculation
    const tacAmount = (subtotal * row.tacPercentage) / 100;

    // Final price calculation based on GST type
    let finalPrice = subtotal + tacAmount;
    if (row.gstType !== 'EXC') {
      finalPrice += gstAmount;
    }

    return {
      basePrice,
      extraCosts,
      subtotal,
      gstAmount,
      tacAmount,
      finalPrice,
      showGstSeparately: row.gstType === 'EXC',
      currency: row.currency || selectedCurrency // Add currency to result
    };
  };

  const updateHotelRow = (index: number, field: keyof HotelRow, value: string | number) => {
    const newRows = [...hotelRows];
    newRows[index] = {
      ...newRows[index],
      [field]: value,
    };

    // Reset all calculated values to 0 if rooms or nights is 0
    if ((field === 'noOfRooms' || field === 'stayNights') &&
        (newRows[index].noOfRooms === 0 || newRows[index].stayNights === 0)) {
      newRows[index].tacAmount = 0;
      newRows[index].gstAmount = 0;
      newRows[index].stayPrice = 0;
    }
    // Calculate normally if both rooms and nights are greater than 0
    else if (field === 'tacPercentage' || field === 'price' ||
             field === 'noOfRooms' || field === 'stayNights' ||
             field === 'gstType') {
      const priceCalc = calculateHotelStayPrice(newRows[index], extraAdults, children);
      newRows[index].tacAmount = priceCalc.tacAmount;
      newRows[index].gstAmount = priceCalc.gstAmount;
      newRows[index].stayPrice = priceCalc.finalPrice;
    }

    setHotelRows(newRows);
  };

  const deleteHotelRow = (index: number) => {
    if (hotelRows.length > 1) {
      const newRows = hotelRows.filter((_, i) => i !== index);
      setHotelRows(newRows);
    }
  };

  const calculateTotals = () => {
    // Existing code for calculating subtotal
    const hotelCosts = hotelRows.map(row => calculateHotelStayPrice(row, extraAdults, children));

    // Convert all amounts to the selected currency for totals
    const convertedHotelCosts = hotelCosts.map(cost => ({
      ...cost,
      finalPrice: convertCurrencyIfNeeded(cost.finalPrice, cost.currency || selectedCurrency, selectedCurrency),
      gstAmount: convertCurrencyIfNeeded(cost.gstAmount, cost.currency || selectedCurrency, selectedCurrency)
    }));

    // Helper function to convert currency if needed
    function convertCurrencyIfNeeded(amount: number, fromCurrency: string, toCurrency: string): number {
      if (fromCurrency === toCurrency) return amount;
      // Use the imported convertCurrency function
      return convertCurrency(amount, fromCurrency, toCurrency);
    }

    // Sum up all hotel costs
    const totalHotelCost = convertedHotelCosts.reduce((sum, cost) => sum + cost.finalPrice, 0);

    // Total extra costs
    const totalExtraAdultCost = hotelRows.reduce((sum, row) => {
      const extraAdultCost = row.extraAdultCost * extraAdults * row.stayNights;
      return sum + convertCurrencyIfNeeded(extraAdultCost, row.currency || selectedCurrency, selectedCurrency);
    }, 0);

    const totalChildrenCost = hotelRows.reduce((sum, row) => {
      const childrenCost = row.childrenCost * children * row.stayNights;
      return sum + convertCurrencyIfNeeded(childrenCost, row.currency || selectedCurrency, selectedCurrency);
    }, 0);

    const totalInfantCost = hotelRows.reduce((sum, row) => {
      const infantCost = row.infantCost * infants * row.stayNights;
      return sum + convertCurrencyIfNeeded(infantCost, row.currency || selectedCurrency, selectedCurrency);
    }, 0);

    // Sum up all other costs
    const otherCostsTotal = Object.values(costs.basicCosts).reduce((sum, cost) => sum + cost, 0) +
      Object.values(costs.addOnCosts).reduce((sum, cost) => sum + cost, 0) +
      Object.values(costs.optionalCosts).reduce((sum, cost) => sum + cost, 0);

    // Calculate initial subtotal
    const initialSubtotal = totalHotelCost + otherCostsTotal;

    // Calculate discount amount
    let discountAmount = 0;

    if (discountMode === 'manual' && discountValue > 0) {
      if (discountType === 'percentage') {
        discountAmount = initialSubtotal * (discountValue / 100);
      } else {
        discountAmount = discountValue;
      }
    } else if (discountMode === 'promo' && selectedPromoCode) {
      const promoCode = promoCodes.find(p => p.code === selectedPromoCode);
      if (promoCode) {
        if (promoCode.type === 'percentage') {
          discountAmount = initialSubtotal * (promoCode.value / 100);
        } else {
          discountAmount = promoCode.value;
        }
      }
    }

    // Ensure discount doesn't exceed subtotal
    discountAmount = Math.min(discountAmount, initialSubtotal);

    // Apply discount to subtotal
    const subtotal = initialSubtotal - discountAmount;

    // Calculate commission and GST on discounted subtotal
    const commissionRate = commission === 'custom' ? customCommission : (commission as number);
    const commissionAmount = subtotal * (commissionRate / 100); // Commission based on selected rate
    const totalWithCommission = subtotal + commissionAmount;
    const gst = totalWithCommission * 0.05; // 5% GST
    const grandTotal = totalWithCommission + gst;

    // Calculate per person cost
    const totalPax = noOfPersons + extraAdults;
    const perPersonCost = totalPax > 0 ? totalWithCommission / totalPax : 0;

    // Calculate excluded GST
    const excludedGst = hotelCosts
      .filter(cost => cost.showGstSeparately)
      .reduce((sum, cost) => sum + cost.gstAmount, 0);

    // Helper function to round numbers
    const roundTo = (value: number, decimals: number = 0): number => {
      const factor = Math.pow(10, decimals);
      return Math.round(value * factor) / factor;
    };

    return {
      hotelCosts,
      totalHotelCost,
      otherCostsTotal,
      initialSubtotal,
      discount: discountAmount,
      subtotal,
      commissionAmount,
      gst,
      grandTotal: roundTo(grandTotal),
      perPersonCost: roundTo(perPersonCost),
      totalPax,
      excludedGst,
      totalExtraAdultCost,
      totalChildrenCost,
      totalInfantCost,
    };
  };

  const resetForm = () => {
    setCurrentQuoteId(null);
    setHotelRows([{
      hotelName: '',
      roomType: 'Standard',
      roomCapacity: 2,
      price: 0,
      mealPlan: 'MAP',
      noOfRooms: 0,
      stayNights: 0,
      extraAdultCost: 0,
      childrenCost: 0,
      infantCost: 0,
      gstType: '0',
      tacPercentage: 0,
      tacAmount: 0,
      info: '',
      stayPrice: 0,
      gstAmount: 0,
      currency: selectedCurrency
    }]);
    setCosts({
      basicCosts: {
        meals: 0,
        transportation: 0,
        cabSightseeing: 0,
        trainCost: 0,
        ferryCost: 0,
        parkingToll: 0,
      },
      addOnCosts: {
        addOnActivity: 0,
        marketing: 0,
        addOn: 0,
      },
      optionalCosts: {
        flightTicket: 0,
        guideWages: 0,
      }
    });
    setPackageName('');
    setCustomerName('');
    setRecipientPhoneNumber(''); // Clear customer mobile
    setRecipientEmail(''); // Clear customer email
    setDestination('');
    setValidityDate('');
    setSeason('');
    setCabType('');
    setQuoteType('');
    setTripDuration('');
    setTripType('');
    setPackageType('');
    setCommission(5);
    setCustomCommission(0);
  };

  const sendEmail = async () => {
    if (!recipientEmail) {
      alert('Please enter recipient email address');
      return;
    }

    setIsEmailSending(true);
    try {
      // Initialize EmailJS
      emailjs.init(EMAIL_CONFIG.USER_ID);

      // Prepare template parameters
      const templateParams = {
        to_email: recipientEmail,
        from_name: "TripXplo Travel",
        to_name: recipientEmail.split('@')[0],
        subject: `Travel Quote: ${packageName} - ${destination}`,
        package_name: packageName,
        destination: destination,
        trip_duration: tripDuration,
        travel_dates: `${quoteDate} to ${validityDate}`,
        rooms: hotelRows.reduce((sum, row) => sum + row.noOfRooms, 0),
        pax: `${noOfPersons + extraAdults} Adults, ${children} Children`,
        total_price: formatPrice(calculateTotals().grandTotal),
        hotels: hotelRows.map((row, index) =>
          `${index + 1}. ${row.hotelName} (${row.roomType}, ${row.noOfRooms} rooms)`
        ).join('\n'),
        email: recipientEmail, // Add this explicitly
        recipient: recipientEmail // Add this as backup
      };

      // Send email
      const response = await emailjs.send(
        EMAIL_CONFIG.SERVICE_ID,
        EMAIL_CONFIG.TEMPLATE_ID,
        templateParams
      );

      if (response.status === 200) {
        alert('Quote details sent successfully to ' + recipientEmail);
      } else {
        throw new Error(`Email failed with status: ${response.status}`);
      }

    } catch (error) {
      console.error('Email error:', error);
      alert('Failed to send email. Please check if the email address is valid.');
    } finally {
      setIsEmailSending(false);
    }
  };

  const generateWhatsAppMessage = () => {
    const totalCost = formatPrice(calculateTotals().grandTotal);

    // Format the date range
    const formatDateRange = () => {
      if (!quoteDate || !validityDate) return '';
      const startDate = new Date(quoteDate);
      const endDate = new Date(validityDate);
      const startMonth = startDate.toLocaleString('default', { month: 'long' });
      const endMonth = endDate.toLocaleString('default', { month: 'long' });
      return `${startMonth}, ${endMonth}`;
    };

    // Format hotel details
    const formatHotelDetails = () => {
      return hotelRows.map((row, _index) =>
        `${row.stayNights}N - ${row.hotelName}/ Similar - (${row.noOfRooms} - Rooms - ${row.mealPlan})`
      ).join('\n');
    };

    // Fetch dynamic inclusions and exclusions
    const inclusions = getActiveInclusions();
    const exclusions = getActiveExclusions();

    // Emoji mapping for inclusions (add more as needed)
    const inclusionEmojis: { [key: string]: string } = {
      'welcome': '🥤',
      'breakfast': '🍲',
      'cab': '🚕',
      'insurance': '🏥',
      'shikara': '🛶',
      'meal': '🍽️',
      'dinner': '🍲',
      'hotel': '🏨',
      'sightseeing': '🏞️',
      'guide': '🧑‍💼',
      'entry': '🎫',
      'activity': '🎯',
      'pickup': '🚗',
      'drop': '🛬',
      'transfer': '🚖',
      'ticket': '🎟️',
      'water': '💧',
      'snacks': '🍪',
      'wifi': '📶',
      'room': '🛏️',
      'pool': '🏊',
      'spa': '💆',
      'tax': '💸',
    };

    // Emoji mapping for exclusions (add more as needed)
    const exclusionEmojis: { [key: string]: string } = {
      'flight': '✈️',
      'train': '🚆',
      'personal': '👛',
      'adventure': '🧗',
      'sightseeing': '🏞️',
      'museum': '🏛️',
      'entry': '🎫',
      'insurance': '🏥',
      'snow': '❄️',
      'vehicle': '🚙',
      'laundry': '🧺',
      'internet': '🌐',
      'telephone': '📞',
      'expenses': '💰',
      'fees': '💵',
      'anything': '🔸',
    };

    // Helper to add emoji if keyword matches
    const getEmoji = (text: string, emojiMap: { [key: string]: string }) => {
      for (const key in emojiMap) {
        if (text.toLowerCase().includes(key)) return emojiMap[key];
      }
      return '•';
    };

    // Format inclusions/exclusions for WhatsApp message
    const formattedInclusions = inclusions.length
      ? inclusions.map(item => {
          const emoji = getEmoji(item, inclusionEmojis);
          return `${emoji} ${item.replace(/^•\s*/, '')}`;
        }).join('\n')
      : '—';
    const formattedExclusions = exclusions.length
      ? exclusions.map(item => {
          const emoji = getEmoji(item, exclusionEmojis);
          return `${emoji} ${item.replace(/^•\s*/, '')}`;
        }).join('\n')
      : '—';

    const message = [
      `📍 *${tripDuration} - ${destination}*`,
      `🗾 *${hotelRows.map(row => `${row.stayNights}N ${row.hotelName}`).join(', ')}*`,
      `🗓️ *Date of Travel* : ${formatDateRange()}`,
      `✨ *Stay* : ${tripDuration}`,
      `🛏️ *Rooms*: ${hotelRows.reduce((sum, row) => sum + row.noOfRooms, 0)}`,
      `👥 *Pax* : (${noOfPersons} Adult${noOfPersons > 1 ? 's' : ''} + ${children} Children <11 Yrs)`,
      ``, // blank line above final price
      `💵 *Final Price* : *${totalCost}* Inc GST`,
      ``,
      `🎉 *Inclusions*`,
      ``, // blank line after inclusions heading
      formattedInclusions,
      ``,
      `🏨 *Hotels Included*`,
      ``, // blank line after Hotels Included heading
      formatHotelDetails(),
      ``,
      `❌ *Exclusions*`, // cross mark instead of danger symbol
      ``, // blank line after exclusions heading
      formattedExclusions,
      ``,
      `~ Team, TripXplo by Tripmilestone (P) Ltd`
    ].join('\n');

    return encodeURIComponent(message).replace(/%(09|0[A-F]|1[A-F]|7F)/g,
      (s) => `%${s.slice(-2)}`);
  };

  const sendWhatsAppMessage = async () => {
    // Auto-populate from customer mobile if recipient phone is empty
    let phoneToUse = recipientPhoneNumber;
    if (!phoneToUse && recipientPhoneNumber) {
      phoneToUse = recipientPhoneNumber;
      setRecipientPhoneNumber(recipientPhoneNumber);
    }

    if (!phoneToUse) {
      alert('Please enter a customer mobile number in the Package Details section');
      return;
    }

    setIsWhatsAppSending(true);
    try {
      // Auto-trim whitespace and remove all non-numeric characters
      const cleanNumber = phoneToUse.trim().replace(/\D/g, '');
      
      // Validate mobile number (should be 10 digits for Indian numbers)
      if (cleanNumber.length !== 10 || !['6', '7', '8', '9'].includes(cleanNumber[0])) {
        alert('Please enter a valid 10-digit Indian mobile number');
        setIsWhatsAppSending(false);
        return;
      }

      const message = generateWhatsAppMessage();

      // Add country code for WhatsApp
      const phoneWithCountryCode = `91${cleanNumber}`;

      // WhatsApp Web URL (default option)
      const webUrl = `https://web.whatsapp.com/send?phone=${phoneWithCountryCode}&text=${message}`;

      // Try opening WhatsApp Web
      const webWindow = window.open(webUrl, '_blank');

      // Handle cases where the window was blocked or failed to open
      if (!webWindow || webWindow.closed || typeof webWindow.closed === 'undefined') {
        // If Web version fails, try the mobile URL as fallback
        window.location.href = `whatsapp://send?phone=${phoneWithCountryCode}&text=${message}`;
      }

      // Update lead status when WhatsApp is sent
      if (phoneToUse && customerName) {
        try {
          await updateLeadFromQuoteAction(phoneToUse, 'quote_sent', `Quote sent to ${customerName} for ${destination}`);
        } catch (leadError) {
          console.error('Error updating lead from WhatsApp action:', leadError);
        }
      }

      setTimeout(() => {
        alert(`WhatsApp message sent to ${phoneToUse}! 💬`);
        setIsWhatsAppSending(false);
      }, 1000);
    } catch (error) {
      setIsWhatsAppSending(false);
      alert('Failed to open WhatsApp');
    }
  };

  // PDF Generation Functions
  const handlePrintPreview = async () => {
    try {
      const { generatePDF } = await import('../quotes/utils/pdfGenerator');
      
      const tripDetails = {
        customerName,
        destination,
        packageName,
        quoteDate,
        validityDate,
        noOfPersons,
        children,
        hotelRows,
        calculateTotals,
        familyType: tripType,
        packageId,
        tripDuration,
        packageType,
        currency: selectedCurrency,
        inclusions: getActiveInclusions(),
        exclusions: getActiveExclusions(),
        discount: discountValue,
        discountType
      };

      const pdf = generatePDF(tripDetails);
      
      // Open PDF in a new window for preview
      const pdfBlob = pdf.output('blob');
      const pdfUrl = URL.createObjectURL(pdfBlob);
      window.open(pdfUrl, '_blank');
      
    } catch (error) {
      console.error('Error generating PDF preview:', error);
      alert('Failed to generate PDF preview. Please try again.');
    }
  };

  const handleGeneratePDF = async () => {
    try {
      const { generatePDF } = await import('../quotes/utils/pdfGenerator');
      
      const tripDetails = {
        customerName,
        destination,
        packageName,
        quoteDate,
        validityDate,
        noOfPersons,
        children,
        hotelRows,
        calculateTotals,
        familyType: tripType,
        packageId,
        tripDuration,
        packageType,
        currency: selectedCurrency,
        inclusions: getActiveInclusions(),
        exclusions: getActiveExclusions(),
        discount: discountValue,
        discountType
      };

      const pdf = generatePDF(tripDetails);
      
      // Download the PDF
      const filename = `${packageName || 'Quote'}_${customerName || 'Customer'}_${new Date().toISOString().split('T')[0]}.pdf`;
      pdf.save(filename);
      
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Failed to generate PDF. Please try again.');
    }
  };

  // Calculate the active inclusions and exclusions
  const getActiveInclusions = () => {
    return inclusionItems
      .filter(item => item.isSelected)
      .map(item => item.text.startsWith('•') ? item.text : `• ${item.text}`);
  };

  const getActiveExclusions = () => {
    return exclusionItems
      .filter(item => item.isSelected)
      .map(item => item.text.startsWith('•') ? item.text : `• ${item.text}`);
  };

  // Get active payment options
  // const getActivePaymentOptions = () => {
  //   return paymentOptionItems
  //     .filter(item => item.isSelected)
  //     .map(item => item.text.startsWith('•') ? item.text : `• ${item.text}`);
  // };

  // Get active terms and conditions
  // const getActiveTerms = () => {
  //   return termItems.filter(item => item.isSelected).map(item => item.text);
  // };

  // Generate Family Type Prices Function
  const handleGenerateFamilyTypePrices = async () => {
    try {
      setIsFamilyTypePricesGenerating(true);
      setFamilyTypePricesMessage('');
      setFamilyTypePricesResults([]);

      // Prepare Quote Generator data
      const quoteGeneratorData: QuoteGeneratorData = {
        packageName,
        customerName,
        destination,
        quoteDate,
        validityDate,
        noOfPersons,
        extraAdults,
        children,
        infants,
        hotelRows,
        costs,
        commission: typeof commission === 'number' ? commission : 5,
        discountValue: typeof discountValue === 'number' ? discountValue : 0,
        discountType
      };

      console.log('🚀 Starting Family Type Price Generation...');
      const result = await generateFamilyTypePrices(quoteGeneratorData, currentQuoteId || undefined);

      if (result.success && result.results) {
        setFamilyTypePricesResults(result.results);
        setFamilyTypePricesMessage(result.message);
        console.log('✅ Family Type Prices generated successfully:', result.results.length);
        
        // Show results in a modal or expandable section
        alert(`Family Type Prices Generated Successfully!\n\n${result.results.length} family types calculated.\n\nScroll down to see the results table.`);
      } else {
        setFamilyTypePricesMessage(result.message);
        console.error('❌ Family Type Prices generation failed:', result.message);
        alert(result.message);
      }

    } catch (error) {
      const errorMessage = `Error generating family type prices: ${error instanceof Error ? error.message : 'Unknown error'}`;
      setFamilyTypePricesMessage(errorMessage);
      console.error('❌ Exception in family type price generation:', error);
      alert(errorMessage);
    } finally {
      setIsFamilyTypePricesGenerating(false);
    }
  };

  // Find selected option
  // const findSelectedOption = (options: Option[], value: string): Option | undefined => {
  //   return options.find(option => option.value === value);
  // };

  // User information
  // const userForm = useRef<HTMLFormElement>(null);
  // const [fileLoading, setFileLoading] = useState(false);

  // Add promo code handler
  const handleAddPromoCode = () => {
    if (!newPromoCode.code || newPromoCode.value <= 0) {
      alert('Please enter a valid promo code and value');
      return;
    }

    // Check if code already exists
    if (promoCodes.some(promo => promo.code === newPromoCode.code)) {
      alert('Promo code already exists');
      return;
    }

    // Add new promo code
    setPromoCodes([...promoCodes, { ...newPromoCode }]);

    // Reset form
    setNewPromoCode({
      code: '',
      description: '',
      type: 'percentage',
      value: 0,
      validUntil: ''
    });
  };

  // Delete promo code handler
  const handleDeletePromoCode = (code: string) => {
    setPromoCodes(promoCodes.filter(promo => promo.code !== code));

    // If the deleted code is currently selected, reset selection
    if (selectedPromoCode === code) {
      setSelectedPromoCode('');
    }
  };

  // Generate a new package ID
  const generateNewPackageId = () => {
    return `TXP-${Math.floor(100000 + Math.random() * 900000)}`;
  };

  // Saved quotes interface and state
  interface QuoteListItem {
    id: string;
    package_name: string;
    customer_name: string;
    customer_phone?: string;
    customer_email?: string;
    destination: string;
    created_at: string;
    is_draft: boolean;
    trip_duration?: string;
    family_type?: string;
    validity_date?: string;
    total_cost?: number;
    no_of_persons?: number;
    extra_adults?: number;
    children?: number;
  }

  const [savedQuotes, setSavedQuotes] = useState<QuoteListItem[]>([]);
  const [isLoadingQuotes, setIsLoadingQuotes] = useState(false);
  const [showQuoteModal, setShowQuoteModal] = useState(false);
  // Track the ID of a loaded quote for update operations
  const [currentQuoteId, setCurrentQuoteId] = useState<string | null>(null);

  // Function to fetch saved quotes
  const fetchSavedQuotes = async () => {
    setIsLoadingQuotes(true);
    try {
      if (!supabase) {
        console.error('Supabase client not available');
        setSavedQuotes([]);
        return;
      }

      // Set a timeout for the fetch
      const fetchPromise = supabase
        .from('quotes')
        .select('id, package_name, customer_name, customer_phone, customer_email, destination, created_at, is_draft, trip_duration, family_type, validity_date, total_cost, no_of_persons, extra_adults, children')
        .order('created_at', { ascending: false });

      const timeoutPromise = new Promise<{data: null, error: Error}>((resolve) => {
        setTimeout(() => {
          console.log('Fetch saved quotes timed out');
          resolve({
            data: null,
            error: new Error('Fetch saved quotes timed out')
          });
        }, 8000); // 8 second timeout
      });

      // Race the fetch against the timeout
      const { data, error } = await Promise.race([fetchPromise, timeoutPromise]);

      if (error) {
        console.error('Error fetching saved quotes:', error);
        setSavedQuotes([]);
        return;
      }

      setSavedQuotes(data || []);
    } catch (error) {
      console.error('Exception fetching saved quotes:', error);
      setSavedQuotes([]);
    } finally {
      setIsLoadingQuotes(false);
    }
  };

  // Function to save current quote
  const saveQuote = async (isDraft = true) => {
    try {
      if (!supabase) {
        alert('Database connection not available. Please try again later.');
        return;
      }

      // Generate a new package ID if this is a new quote
      if (!currentQuoteId) {
        setPackageId(generateNewPackageId());
      }

      // Prepare the quote data
      const quoteData = {
        package_name: packageName,
        customer_name: customerName,
        customer_phone: recipientPhoneNumber || null, // Add mobile number for mapping
        customer_email: recipientEmail || null, // Add email for customer identification
        destination: destination,
        trip_duration: tripDuration,
        family_type: tripType,
        validity_date: validityDate,
        no_of_persons: noOfPersons,
        extra_adults: extraAdults,
        children: children,
        infants: infants,
        total_cost: calculateTotals().grandTotal,
        is_draft: isDraft,
        package_type: packageType,
        plan: plan,
        season: season,
        quote_type: quoteType,
        currency: selectedCurrency,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        commission_rate: commission,
        custom_commission: customCommission
      };

      let quoteId;
      if (currentQuoteId) {
        // Update existing quote
        const { error: updateError } = await supabase
          .from('quotes')
          .update(quoteData)
          .eq('id', currentQuoteId);

        if (updateError) throw updateError;
        quoteId = currentQuoteId;
      } else {
        // Insert new quote
        const { data, error: insertError } = await supabase
          .from('quotes')
          .insert(quoteData)
          .select();

        if (insertError) throw insertError;
        quoteId = data[0].id;
      }

      // Save hotel rows
      if (currentQuoteId) {
        // First delete existing hotel rows for this quote
        const { error: deleteError } = await supabase
          .from('hotel_rows')
          .delete()
          .eq('quote_id', currentQuoteId);

        if (deleteError) throw deleteError;
      }

      // Then insert the current hotel rows
      const { error: hotelError } = await supabase
        .from('hotel_rows')
        .insert(
          hotelRows.map(row => ({
            quote_id: quoteId,
            hotel_name: row.hotelName,
            room_type: row.roomType,
            room_capacity: row.roomCapacity,
            price: row.price,
            meal_plan: row.mealPlan,
            no_of_rooms: row.noOfRooms,
            stay_nights: row.stayNights,
            extra_adult_cost: row.extraAdultCost,
            children_cost: row.childrenCost,
            infant_cost: row.infantCost,
            gst_type: row.gstType,
            tac_percentage: row.tacPercentage,
            tac_amount: row.tacAmount,
            info: row.info,
            stay_price: row.stayPrice,
            gst_amount: row.gstAmount,
            currency: row.currency
          }))
        );

      if (hotelError) throw hotelError;

      // Save costs: delete existing cost and insert new record
      if (currentQuoteId) {
        const { error: deleteCostError } = await supabase
          .from('costs')
          .delete()
          .eq('quote_id', currentQuoteId);
        if (deleteCostError) throw deleteCostError;
      }
      const { error: insertCostError } = await supabase
        .from('costs')
        .insert([
          {
            quote_id: quoteId,
            meals: costs.basicCosts.meals,
            transportation: costs.basicCosts.transportation,
            cab_sightseeing: costs.basicCosts.cabSightseeing,
            train_cost: costs.basicCosts.trainCost,
            ferry_cost: costs.basicCosts.ferryCost,
            parking_toll: costs.basicCosts.parkingToll,
            add_on_activity: costs.addOnCosts.addOnActivity,
            marketing: costs.addOnCosts.marketing,
            add_on: costs.addOnCosts.addOn,
            flight_ticket: costs.optionalCosts.flightTicket,
            guide_wages: costs.optionalCosts.guideWages
          }
        ]);
      if (insertCostError) throw insertCostError;

      // IMPORTANT: For existing quotes, you may need to manually clean up duplicate rows
      // in the costs table in your Supabase database for this fix to work correctly.

      // Save checklist items - handle errors gracefully
      try {
        if (currentQuoteId) {
          // Delete existing checklist items
          await supabase
            .from('checklist_items')
            .delete()
            .eq('quote_id', currentQuoteId);
        }

        // Insert new checklist items
        const checklistItems = [
          ...inclusionItems.map(item => ({
            quote_id: quoteId,
            text: item.text,
            is_selected: item.isSelected,
            type: 'inclusion'
          })),
          ...exclusionItems.map(item => ({
            quote_id: quoteId,
            text: item.text,
            is_selected: item.isSelected,
            type: 'exclusion'
          })),
          ...paymentOptionItems.map(item => ({
            quote_id: quoteId,
            text: item.text,
            is_selected: item.isSelected,
            type: 'payment_option'
          })),
          ...termItems.map(item => ({
            quote_id: quoteId,
            text: item.text,
            is_selected: item.isSelected,
            type: 'term'
          }))
        ];

        if (checklistItems.length > 0) {
          await supabase
            .from('checklist_items')
            .insert(checklistItems);
        }
      } catch (checklistError) {
        console.error('Error saving checklist items:', checklistError);
        // Continue with the save operation even if checklist items fail
      }

      // Handle promo codes - try to save but don't fail if it errors
      try {
        if (currentQuoteId) {
          // Delete existing promo codes
          await supabase
            .from('promo_codes')
            .delete()
            .eq('quote_id', currentQuoteId);
        }

        // Insert new promo codes
        if (promoCodes.length > 0) {
          await supabase
            .from('promo_codes')
            .insert(
              promoCodes.map(code => ({
                quote_id: quoteId,
                code: code.code,
                description: code.description,
                type: code.type,
                value: code.value,
                valid_until: code.validUntil
              }))
            );
        }
      } catch (promoError) {
        console.error('Error saving promo codes:', promoError);
        // Continue with the save operation even if promo codes fail
      }

      // Refresh the saved quotes list
      await fetchSavedQuotes();

      // Sync with leads if mobile number is provided
      if (recipientPhoneNumber && customerName) {
        try {
          await syncQuoteWithLead({
            quote_id: quoteId,
            customer_phone: recipientPhoneNumber,
            customer_name: customerName,
            customer_email: recipientEmail || undefined,
            destination: destination,
            total_cost: calculateTotals().grandTotal,
          });
        } catch (syncError) {
          console.error('Error syncing with leads:', syncError);
          // Don't fail the save operation if sync fails
        }
      }

      // Show success message
      if (currentQuoteId) {
        alert("Quote updated successfully!");
      } else {
        alert("Quote saved as draft successfully!");
      }

      // Reset form if this was a new quote
      if (!currentQuoteId) {
        resetForm();
      }
    } catch (err) {
      console.error('Error saving quote:', err);
      alert('Failed to save quote. Please try again.');
    }
  };

  // Function to load a quote
  const loadQuote = async (quoteId: string) => {
    try {
      if (!supabase) {
        alert('Database connection not available. Please try again later.');
        return;
      }

      console.log('Loading quote with ID:', quoteId);
      console.log('Attempting to load quote ID:', quoteId);

      // Set a timeout for the fetch
      const quotePromise = supabase
        .from('quotes')
        .select('*')
        .eq('id', quoteId)
        .single();

      const timeoutPromise = new Promise<{data: null, error: Error}>((resolve) => {
        setTimeout(() => {
          console.log('Fetch quote details timed out');
          resolve({
            data: null,
            error: new Error('Fetch quote details timed out')
          });
        }, 8000); // 8 second timeout
      });

      // Race the fetch against the timeout
      const { data: quoteData, error: quoteError } = await Promise.race([quotePromise, timeoutPromise]);

      if (quoteError) throw quoteError;
      console.log('Quote data loaded:', quoteData);

      // Fetch hotel rows and map to our HotelRow interface
      const { data: hotelRowsFromDb, error: hotelError } = await supabase
        .from('hotel_rows')
        .select('*')
        .eq('quote_id', quoteId);
      if (hotelError) throw hotelError;
      console.log('Hotel rows loaded:', hotelRowsFromDb);

      // Fetch costs data
      const { data: costsFromDb, error: costsError } = await supabase
        .from('costs')
        .select('*')
        .eq('quote_id', quoteId)
        .single();
      if (costsError && costsError.code !== 'PGRST116') throw costsError; // Ignore not found error
      console.log('Data received for quote ID ' + quoteId + ':', { quoteData, hotelRowsFromDb, costsFromDb });

      // Fetch checklist items
      const { data: checklistFromDb, error: checklistError } = await supabase
        .from('checklist_items')
        .select('*')
        .eq('quote_id', quoteId);
      if (checklistError) throw checklistError;

      // Fetch promo codes with error handling
      let promoCodesFromDb: any[] | null = null;
      try {
        const { data, error } = await supabase
          .from('promo_codes')
          .select('*')
          .eq('quote_id', quoteId);
        if (error) throw error;
        promoCodesFromDb = data;
      } catch (promoError) {
        console.error('Error loading promo codes for quote ' + quoteId + ':', promoError);
        setPromoCodes(DEFAULT_PROMO_CODES);
      }
      // Update promo codes from fetched data or default
      if (promoCodesFromDb) {
        if (promoCodesFromDb.length > 0) {
          const loadedPromoCodes = promoCodesFromDb.map(promo => ({
            code: promo.code,
            description: promo.description,
            type: promo.type,
            value: promo.value,
            validUntil: promo.valid_until
          }));
          setPromoCodes(loadedPromoCodes);
        } else {
          setPromoCodes(DEFAULT_PROMO_CODES);
        }
      }

      // Map hotel rows to our interface
      const loadedHotelRows: HotelRow[] = (hotelRowsFromDb || []).map((r: any) => ({
        hotelName: r.hotel_name,
        roomType: r.room_type,
        roomCapacity: r.room_capacity,
        price: r.price,
        mealPlan: r.meal_plan,
        noOfRooms: r.no_of_rooms,
        stayNights: r.stay_nights,
        extraAdultCost: r.extra_adult_cost,
        childrenCost: r.children_cost,
        infantCost: r.infant_cost,
        gstType: r.gst_type as HotelRow['gstType'],
        tacPercentage: r.tac_percentage,
        tacAmount: r.tac_amount,
        info: r.info,
        stayPrice: r.stay_price,
        gstAmount: r.gst_amount,
        currency: r.currency || selectedCurrency
      }));

      // Update all state variables from loaded quote data
      if (quoteData) {
        // Update basic quote info
        setPackageId(quoteData.package_id || 'PKG-' + Math.random().toString(36).substring(2, 9));
        setPackageName(quoteData.package_name || '');
        setCustomerName(quoteData.customer_name || '');
        setRecipientPhoneNumber(quoteData.customer_phone || ''); // Load customer mobile
        setRecipientEmail(quoteData.customer_email || ''); // Load customer email
        setDestination(quoteData.destination || '');
        setQuoteDate(quoteData.quote_date || new Date().toISOString().split('T')[0]);
        setValidityDate(quoteData.validity_date || '');
        setSeason(quoteData.season || '');
        setCabType(quoteData.cab_type || '');
        // setCabSeating(quoteData.cab_seating || '');
        setQuoteType(quoteData.quote_type || '');
        setTripDuration(quoteData.trip_duration || '');
        // Use tripType instead of familyType
        if (quoteData.family_type) {
          setTripType(quoteData.family_type || '');
        }
        setPackageType(quoteData.package_type || '');

        // Update people info
        setNoOfPersons(quoteData.no_of_persons || 2);
        setExtraAdults(quoteData.extra_adults || 0);
        setChildren(quoteData.children || 0);
        setInfants(quoteData.infants || 0);

        // Update pricing info
        setPlan(quoteData.plan || 'Silver');

        // Load the commission rate correctly
        if (quoteData.commission_rate) {
          console.log('Loading commission rate:', quoteData.commission_rate, 'type:', typeof quoteData.commission_rate);

          // Find the matching commission option to ensure correct type
          const matchingOption = COMMISSION_OPTIONS.find(
            option => {
              console.log('Comparing:', option.value, '(', typeof option.value, ') with', quoteData.commission_rate, '(', typeof quoteData.commission_rate, ')');
              return String(option.value) === String(quoteData.commission_rate);
            }
          );

          if (matchingOption) {
            console.log('Found matching commission option:', matchingOption);
            setCommission(matchingOption.value as CommissionValue);
          } else {
            // Default to first option if not found
            console.log('No matching commission option found, defaulting to:', COMMISSION_OPTIONS[0].value);
            setCommission(COMMISSION_OPTIONS[0].value as CommissionValue);
          }
        } else {
          console.log('No commission_rate found in quote data');
          setCommission(COMMISSION_OPTIONS[0].value as CommissionValue);
        }
        setCustomCommission(quoteData.custom_commission || 0);

        // Set the currency
        if (quoteData.currency) {
          setSelectedCurrency(quoteData.currency);
          setActiveCurrency(quoteData.currency);
        }

        // Update discount info
        setDiscountType(quoteData.discount_type || 'percentage');
        setDiscountValue(quoteData.discount_value || 0);
        if (quoteData.discount_mode) {
          // Safely set discount mode if it exists in state
          setDiscountMode(quoteData.discount_mode || 'manual');
        }
        if (quoteData.selected_promo_code) {
          setSelectedPromoCode(quoteData.selected_promo_code || '');
        }
      }

      // Reset hotel rows and costs before applying new quote data
      setHotelRows([]);
      setCosts(initialCostsState);
      // Update hotel rows
      setHotelRows(loadedHotelRows);
      // Merge loaded cost data with default structure
      const defaultCosts = initialCostsState;
      const loadedCostsData = costsFromDb
        ? {
            basicCosts: {
              meals: costsFromDb.meals,
              transportation: costsFromDb.transportation,
              cabSightseeing: costsFromDb.cab_sightseeing,
              trainCost: costsFromDb.train_cost,
              ferryCost: costsFromDb.ferry_cost,
              parkingToll: costsFromDb.parking_toll
            },
            addOnCosts: {
              addOnActivity: costsFromDb.add_on_activity,
              marketing: costsFromDb.marketing,
              addOn: costsFromDb.add_on
            },
            optionalCosts: {
              flightTicket: costsFromDb.flight_ticket,
              guideWages: costsFromDb.guide_wages
            }
          }
        : {};
      const mergedCosts = {
        basicCosts: { ...defaultCosts.basicCosts, ...(loadedCostsData.basicCosts || {}) },
        addOnCosts: { ...defaultCosts.addOnCosts, ...(loadedCostsData.addOnCosts || {}) },
        optionalCosts: { ...defaultCosts.optionalCosts, ...(loadedCostsData.optionalCosts || {}) }
      };
      console.log('Setting costs state with merged data for quote ' + quoteId + ':', mergedCosts);
      setCosts(mergedCosts);

      // Update checklist items
      if (checklistFromDb && checklistFromDb.length > 0) {
        const inclusionItems = checklistFromDb
          .filter((item: any) => item.item_type === 'inclusion')
          .map((item: any) => ({ id: item.id, text: item.text, isSelected: item.is_selected }));

        const exclusionItems = checklistFromDb
          .filter((item: any) => item.item_type === 'exclusion')
          .map((item: any) => ({ id: item.id, text: item.text, isSelected: item.is_selected }));

        const termItems = checklistFromDb
          .filter((item: any) => item.item_type === 'term')
          .map((item: any) => ({ id: item.id, text: item.text, isSelected: item.is_selected }));

        setInclusionItems(inclusionItems);
        setExclusionItems(exclusionItems);
        setTermItems(termItems);
        // Also update payment option checklist items
        const paymentOptionItems = checklistFromDb
          .filter((item: any) => item.item_type === 'payment_option')
          .map((item: any) => ({ id: item.id, text: item.text, isSelected: item.is_selected }));
        setPaymentOptionItems(paymentOptionItems);
      }

      // Close the modal after successful loading
      setShowQuoteModal(false);

      // Track that we are editing an existing quote
      setCurrentQuoteId(quoteId);

      alert('Quote loaded successfully!');
    } catch (error) {
      console.error('Error loading quote:', error);
      alert('Failed to load quote');
    }
  };

  // Function to clone a quote
  const cloneQuote = async (quoteId: string) => {
    await loadQuote(quoteId);
    // Clear currentQuoteId so that saveQuote will insert a new quote
    setCurrentQuoteId(null);
    // Generate a new package ID for the cloned quote
    setPackageId(generateNewPackageId());
    // Reset some fields that should be unique for a new quote
    setCustomerName('');
    alert('Quote cloned! Add a new customer name and save as a new quote.');
  };

  // Function to delete a quote
  const deleteQuote = async (quoteId: string) => {
    try {
      if (!supabase) {
        alert('Database connection not available. Please try again later.');
        return;
      }

      setIsLoadingQuotes(true);

      // Set a timeout for the delete operation
      const deletePromise = supabase
        .from('quotes')
        .delete()
        .eq('id', quoteId);

      const timeoutPromise = new Promise<{data: null, error: Error}>((resolve) => {
        setTimeout(() => {
          console.log('Delete quote operation timed out');
          resolve({
            data: null,
            error: new Error('Delete quote operation timed out')
          });
        }, 8000); // 8 second timeout
      });

      // Race the delete operation against the timeout
      const { error } = await Promise.race([deletePromise, timeoutPromise]);

      if (error) throw error;

      // Also delete related data (hotel rows, costs, checklist items)
      try {
        await Promise.all([
          supabase.from('hotel_rows').delete().eq('quote_id', quoteId),
          supabase.from('costs').delete().eq('quote_id', quoteId),
          supabase.from('checklist_items').delete().eq('quote_id', quoteId),
          supabase.from('promo_codes').delete().eq('quote_id', quoteId)
        ]);
      } catch (relatedDataError) {
        console.error('Error deleting related data:', relatedDataError);
        // Continue with the operation even if related data deletion fails
      }

      // Refresh the list of quotes
      try {
        await fetchSavedQuotes();
      } catch (fetchError) {
        console.error('Error refreshing quotes list:', fetchError);
      }

      alert('Quote deleted successfully');
    } catch (error) {
      console.error('Error deleting quote:', error);
      alert('Failed to delete quote');
    } finally {
      setIsLoadingQuotes(false);
    }
  };

  // Add a useEffect to fetch quotes when component mounts or when activeMainTab changes to saved-quotes
  useEffect(() => {
    if (activeMainTab === 'saved-quotes' || showQuoteModal) {
      console.log('Triggering fetch saved quotes due to tab change or modal open');
      fetchSavedQuotes();
    }
  }, [activeMainTab, showQuoteModal]);

  // Add a SavedQuotesModal component
  const SavedQuotesModal = () => {
    // Debug log to verify quotes are available
    console.log('SavedQuotesModal rendered, quotes:', savedQuotes);

    if (!showQuoteModal) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 w-11/12 max-w-4xl max-h-[80vh] overflow-auto">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold text-gray-800">Saved Quotes</h2>
            <button
              onClick={() => setShowQuoteModal(false)}
              className="text-gray-500 hover:text-gray-700"
            >
              <X size={24} />
            </button>
          </div>

          {/* Debug info section */}
          <div className="mb-4 p-2 bg-gray-100 rounded text-xs">
            <p>Debug: {savedQuotes.length} quotes loaded | Loading: {isLoadingQuotes ? 'Yes' : 'No'}</p>
          </div>

          {isLoadingQuotes ? (
            <div className="flex justify-center py-16">
              <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-[#00B69B]"></div>
            </div>
          ) : (
            <>
              {savedQuotes.length === 0 ? (
                <p className="text-center py-8 text-gray-500">No saved quotes found.</p>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Package Name
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Customer
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Destination
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Date
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {savedQuotes.map((quote) => (
                        <tr key={quote.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">{quote.package_name || 'Untitled'}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-500">{quote.customer_name || 'N/A'}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-500">{quote.destination || 'N/A'}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-500">
                              {new Date(quote.created_at).toLocaleDateString()}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                              quote.is_draft ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'
                            }`}>
                              {quote.is_draft ? 'Draft' : 'Final'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button
                              onClick={() => {
                                loadQuote(quote.id);
                                setShowQuoteModal(false);
                                setActiveMainTab('new-quote');
                              }}
                              className="text-[#00B69B] hover:text-[#008577] mr-3"
                            >
                              Load
                            </button>
                            <button
                              onClick={() => {
                                cloneQuote(quote.id);
                                setShowQuoteModal(false);
                                setActiveMainTab('new-quote');
                              }}
                              className="text-blue-600 hover:text-blue-900 mr-3"
                            >
                              Clone
                            </button>
                            <button
                              onClick={async () => {
                                if (confirm('Are you sure you want to delete this quote?')) {
                                  deleteQuote(quote.id);
                                }
                              }}
                              className="text-red-600 hover:text-red-900"
                            >
                              Delete
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    );
  };

  // New component for Saved Quotes list in the tab view
  const SavedQuotesTabView = () => {
    // const [expandedQuoteId, setExpandedQuoteId] = useState<string | null>(null);
    // const [expandedDetails, setExpandedDetails] = useState<any>({});
    // Local search state for filtering saved quotes
    const [searchTerm, setSearchTerm] = useState<string>('');
    // Pagination state
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [itemsPerPage, setItemsPerPage] = useState<number>(10);
    // Collapsible state for customer groups
    const [collapsedCustomers, setCollapsedCustomers] = useState<Set<string>>(new Set());
    // Filter quotes by destination, customer name, phone, or email
    const filteredQuotes = savedQuotes.filter((quote) => {
      const term = searchTerm.toLowerCase();
      return (
        (quote.destination || '').toLowerCase().includes(term) ||
        (quote.customer_name || '').toLowerCase().includes(term) ||
        (quote.customer_phone || '').toLowerCase().includes(term) ||
        (quote.customer_email || '').toLowerCase().includes(term)
      );
    });

    // Pagination calculations (removed - now using customer-based pagination)

    // Reset to page 1 when search term or items per page changes
    useEffect(() => {
      setCurrentPage(1);
      setCollapsedCustomers(new Set()); // Reset collapsed state when search changes
    }, [searchTerm, itemsPerPage]);

    // Handle page change
    const handlePageChange = (page: number) => {
      if (page < 1 || page > totalPages) return;
      setCurrentPage(page);
    };

    // Handle items per page change
    const handleItemsPerPageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
      setItemsPerPage(Number(e.target.value));
    };

    // Fetch hotel details for expanded quote
    // const handleExpand = async (quote: any) => {
    //   if (expandedQuoteId === quote.id) {
    //     setExpandedQuoteId(null);
    //     return;
    //   }
    //   setExpandedQuoteId(quote.id);
    //   // Fetch hotel rows if not already loaded
    //   if (!expandedDetails[quote.id]) {
    //     const { data: hotelRows } = await supabase
    //       .from('hotel_rows')
    //       .select('hotel_name, stay_nights')
    //       .eq('quote_id', quote.id);
    //     setExpandedDetails((prev: any) => ({ ...prev, [quote.id]: hotelRows || [] }));
    //   }
    // };

    // Helper to format hotel details
    // const formatHotelDetails = (hotelRows: any[]) => {
    //   if (!hotelRows || hotelRows.length === 0) return 'No hotel details';
    //   return hotelRows.map(row => `${row.stay_nights}N - ${row.hotel_name}`).join(', ');
    // };

    // Helper to format pax
    const formatPax = (quote: any) => {
      const adults = (quote.no_of_persons || 0) + (quote.extra_adults || 0);
      const children = quote.children || 0;
      return `${adults} Adult${adults > 1 ? 's' : ''}${children ? `, ${children} Child${children > 1 ? 'ren' : ''}` : ''}`;
    };

    // Helper to format date in DD-MMM-YYYY format
    const formatDateDisplay = (dateString: string) => {
      if (!dateString) return '-';
      const date = new Date(dateString);
      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 
                     'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      const day = date.getDate().toString().padStart(2, '0');
      const month = months[date.getMonth()];
      const year = date.getFullYear();
      return `${day}-${month}-${year}`;
    };

    // Group quotes by customer mobile number
    const groupQuotesByCustomer = (quotes: QuoteListItem[]) => {
      const grouped: { [key: string]: QuoteListItem[] } = {};
      
      quotes.forEach(quote => {
        const key = quote.customer_phone || quote.customer_name || 'Unknown Customer';
        if (!grouped[key]) {
          grouped[key] = [];
        }
        grouped[key].push(quote);
      });
      
      return grouped;
    };

    const groupedQuotes = groupQuotesByCustomer(filteredQuotes);
    
    // Pagination for customer groups
    const customerKeys = Object.keys(groupedQuotes);
    const totalPages = Math.ceil(customerKeys.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = Math.min(startIndex + itemsPerPage, customerKeys.length);
    const paginatedCustomerKeys = customerKeys.slice(startIndex, endIndex);

    // Toggle customer collapse
    const toggleCustomerCollapse = (customerKey: string) => {
      const newCollapsed = new Set(collapsedCustomers);
      if (newCollapsed.has(customerKey)) {
        newCollapsed.delete(customerKey);
      } else {
        newCollapsed.add(customerKey);
      }
      setCollapsedCustomers(newCollapsed);
    };

    return (
      <div className="bg-white rounded-lg shadow p-6">
        {/* Header section */}
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold text-gray-800">My Saved Quotes</h2>
          <div className="flex items-center gap-2">
            {/* Refresh & New Quote buttons */}
            <button onClick={() => fetchSavedQuotes()} className="px-4 py-2 flex items-center gap-2 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"><RotateCcw size={14} />Refresh</button>
            <button onClick={() => setActiveMainTab('new-quote')} className="px-4 py-2 bg-[#00B69B] text-white rounded-md hover:bg-[#008577] transition-colors">New Quote</button>
          </div>
        </div>
        {/* Enhanced Search input for filtering quotes */}
        <div className="mb-4">
          <div className="relative">
            <input
              type="text"
              placeholder="Search by customer name, phone, email, or destination..."
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className="w-full px-4 py-3 pl-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#00B69B] focus:border-[#00B69B]"
            />
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search size={18} className="text-gray-400" />
            </div>
            {searchTerm && (
              <button
                onClick={() => setSearchTerm('')}
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
              >
                <X size={18} />
              </button>
            )}
          </div>
          {searchTerm && (
            <div className="mt-2 text-sm text-gray-600">
              Found {filteredQuotes.length} quote{filteredQuotes.length !== 1 ? 's' : ''} matching "{searchTerm}"
            </div>
          )}
        </div>
        {isLoadingQuotes ? (
          <div className="flex justify-center py-16"><div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-[#00B69B]"></div></div>
        ) : (
          <>
            {filteredQuotes.length === 0 ? (
              <div className="text-center py-16 bg-gray-50 rounded-lg border border-gray-200">
                <div className="text-gray-400 mb-3"><Save size={48} className="mx-auto" /></div>
                <h3 className="text-lg font-medium text-gray-700 mb-2">No Saved Quotes</h3>
                <p className="text-gray-500 mb-4">Create and save your first quote to see it here.</p>
                <button onClick={() => setActiveMainTab('new-quote')} className="px-4 py-2 bg-[#00B69B] text-white rounded-md hover:bg-[#008577] transition-colors">Create New Quote</button>
              </div>
            ) : (
              <div className="space-y-6">
                {paginatedCustomerKeys.map((customerKey) => {
                  const customerQuotes = groupedQuotes[customerKey];
                  const firstQuote = customerQuotes[0];
                  const customerName = firstQuote.customer_name || 'Unknown Customer';
                  const customerPhone = firstQuote.customer_phone;
                  const customerEmail = firstQuote.customer_email;
                  const totalQuotes = customerQuotes.length;
                  const totalValue = customerQuotes.reduce((sum, quote) => sum + (quote.total_cost || 0), 0);
                  const isCollapsed = collapsedCustomers.has(customerKey);
                  
                  return (
                    <div key={customerKey} className="border border-gray-200 rounded-lg overflow-hidden">
                      {/* Customer Header - Collapsible */}
                      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-200">
                        <div className="flex justify-between items-center">
                          <div className="flex items-center space-x-4">
                            <button
                              onClick={() => toggleCustomerCollapse(customerKey)}
                              className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full hover:bg-blue-200 transition-colors"
                            >
                              {isCollapsed ? <ChevronDown size={16} className="text-blue-600" /> : <ChevronUp size={16} className="text-blue-600" />}
                            </button>
                            <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-full">
                              <Users size={20} className="text-blue-600" />
                            </div>
                            <div>
                              <h3 className="text-lg font-semibold text-gray-900">{customerName}</h3>
                              <div className="flex items-center space-x-4 text-sm text-gray-600">
                                {customerPhone && (
                                  <span className="flex items-center">
                                    📱 {customerPhone}
                                  </span>
                                )}
                                {customerEmail && (
                                  <span className="flex items-center">
                                    📧 {customerEmail}
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="flex items-center space-x-4">
                              <div className="text-center">
                                <div className="text-2xl font-bold text-blue-600">{totalQuotes}</div>
                                <div className="text-xs text-gray-500">Quote{totalQuotes !== 1 ? 's' : ''}</div>
                              </div>
                              <div className="text-center">
                                <div className="text-lg font-semibold text-green-600">₹{totalValue.toLocaleString()}</div>
                                <div className="text-xs text-gray-500">Total Value</div>
                              </div>
                              {customerPhone && (
                                <button
                                  onClick={() => {
                                    const cleanNumber = customerPhone.replace(/\D/g, '');
                                    const message = encodeURIComponent(`Hi ${customerName}! I wanted to follow up on your travel quotes. Let me know if you have any questions or would like to proceed with booking.`);
                                    window.open(`https://web.whatsapp.com/send?phone=91${cleanNumber}&text=${message}`, '_blank');
                                  }}
                                  className="px-3 py-1 bg-green-100 text-green-700 rounded-md hover:bg-green-200 transition-colors text-sm"
                                >
                                  📱 WhatsApp
                                </button>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      {/* Quotes List - Collapsible */}
                      {!isCollapsed && (
                        <div className="divide-y divide-gray-100">
                          {customerQuotes.map((quote) => (
                            <div key={quote.id} className="px-6 py-4 hover:bg-gray-50 transition-colors">
                              <div className="flex justify-between items-center">
                                <div className="flex-1 grid grid-cols-1 md:grid-cols-5 gap-4">
                                  <div>
                                    <div className="font-medium text-gray-900">{quote.package_name || 'Untitled'}</div>
                                    <div className="text-sm text-gray-500">{quote.trip_duration || '-'}</div>
                                  </div>
                                  <div>
                                    <div className="text-sm font-medium text-gray-700">{quote.destination || 'N/A'}</div>
                                    <div className="text-xs text-gray-500">{quote.family_type || '-'}</div>
                                  </div>
                                  <div>
                                    <div className="text-sm text-gray-700">₹{quote.total_cost?.toLocaleString() || '0'}</div>
                                    <div className="text-xs text-gray-500">{formatPax(quote)}</div>
                                  </div>
                                  <div>
                                    <div className="text-sm text-gray-700">Valid until</div>
                                    <div className="text-sm font-medium text-blue-600">{formatDateDisplay(quote.validity_date || '')}</div>
                                  </div>
                                  <div>
                                    <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                                      quote.is_draft ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'
                                    }`}>
                                      {quote.is_draft ? 'Draft' : 'Final'}
                                    </span>
                                    <div className="text-xs text-gray-500 mt-1">
                                      {formatDateDisplay(quote.created_at)}
                                    </div>
                                  </div>
                                </div>
                                <div className="flex items-center space-x-2 ml-4">
                                  <button 
                                    onClick={() => { loadQuote(quote.id); setActiveMainTab('new-quote'); }}
                                    className="px-3 py-1 bg-[#00B69B] text-white rounded hover:bg-[#008577] transition-colors text-sm"
                                  >
                                    Load
                                  </button>
                                  <button 
                                    onClick={() => { cloneQuote(quote.id); setActiveMainTab('new-quote'); }}
                                    className="px-3 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors text-sm"
                                  >
                                    Clone
                                  </button>
                                  <button 
                                    onClick={() => deleteQuote(quote.id)}
                                    className="px-3 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors text-sm"
                                  >
                                    Delete
                                  </button>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  );
                })}
                
                {/* Enhanced Pagination */}
                {customerKeys.length > itemsPerPage && (
                  <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                      <div className="flex items-center">
                        <span className="text-sm text-gray-700">
                          Showing <span className="font-medium">{startIndex + 1}</span> to <span className="font-medium">{endIndex}</span> of{' '}
                          <span className="font-medium">{customerKeys.length}</span> customers
                        </span>
                      </div>
                      <div className="flex flex-col sm:flex-row items-center gap-4">
                        <div className="flex items-center">
                          <label htmlFor="itemsPerPage" className="mr-2 text-sm text-gray-700">Customers per page:</label>
                          <select
                            id="itemsPerPage"
                            value={itemsPerPage}
                            onChange={handleItemsPerPageChange}
                            className="border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-[#00B69B]"
                          >
                            <option value={5}>5</option>
                            <option value={10}>10</option>
                            <option value={20}>20</option>
                            <option value={50}>50</option>
                          </select>
                        </div>
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handlePageChange(1)}
                            disabled={currentPage === 1}
                            className={`px-2 py-1 rounded border ${currentPage === 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-100'}`}
                          >
                            &laquo;
                          </button>
                          <button
                            onClick={() => handlePageChange(currentPage - 1)}
                            disabled={currentPage === 1}
                            className={`px-2 py-1 rounded border ${currentPage === 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-100'}`}
                          >
                            &lsaquo;
                          </button>
                          {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                            let pageNum = i + 1;
                            if (totalPages > 5 && currentPage > 3) {
                              pageNum = Math.min(currentPage - 2 + i, totalPages);
                              if (currentPage > totalPages - 2) {
                                pageNum = totalPages - 4 + i;
                              }
                            }

                            return (
                              <button
                                key={pageNum}
                                onClick={() => handlePageChange(pageNum)}
                                className={`px-3 py-1 rounded border ${currentPage === pageNum ? 'bg-[#00B69B] text-white' : 'bg-white text-gray-700 hover:bg-gray-100'}`}
                              >
                                {pageNum}
                              </button>
                            );
                          })}
                          <button
                            onClick={() => handlePageChange(currentPage + 1)}
                            disabled={currentPage === totalPages || totalPages === 0}
                            className={`px-2 py-1 rounded border ${currentPage === totalPages || totalPages === 0 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-100'}`}
                          >
                            &rsaquo;
                          </button>
                          <button
                            onClick={() => handlePageChange(totalPages)}
                            disabled={currentPage === totalPages || totalPages === 0}
                            className={`px-2 py-1 rounded border ${currentPage === totalPages || totalPages === 0 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-100'}`}
                          >
                            &raquo;
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
                
                {/* Summary */}
                <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                  <div className="flex justify-between items-center text-sm text-gray-600">
                    <div>
                      Total: {customerKeys.length} customer{customerKeys.length !== 1 ? 's' : ''} 
                      with {filteredQuotes.length} quote{filteredQuotes.length !== 1 ? 's' : ''}
                    </div>
                    <div>
                      Combined Value: ₹{filteredQuotes.reduce((sum, quote) => sum + (quote.total_cost || 0), 0).toLocaleString()}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    );
  };

  // Show loading indicator while Supabase client is initializing
  if (isClientLoading || !supabase) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center p-8 max-w-md">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#00B69B] mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Loading Quote System</h2>
          <p className="text-gray-600">Please wait while we connect to the database...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Top Navigation Bar */}
      <div className="sticky top-0 z-50 bg-white border-b border-gray-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex">
              <div className="flex-shrink-0 flex items-center">
                <h1 className="text-2xl font-bold text-[#00B69B]">TripXplo Quote</h1>
              </div>
              <div className="hidden sm:ml-6 sm:flex items-center relative group">
                <div className="relative">
                  <button
                    className={`inline-flex items-center px-4 pt-1 border-b-2 text-sm font-medium border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 group-hover:text-[#00B69B]`}
                    type="button"
                  >
                    Quotes
                    <ChevronDown className="ml-1 w-4 h-4" />
                  </button>
                  <div className="absolute left-0 mt-2 w-40 bg-white border border-gray-200 rounded-md shadow-lg opacity-0 group-hover:opacity-100 group-hover:pointer-events-auto pointer-events-none transition-opacity z-50">
                    <button
                      onClick={() => setActiveMainTab('new-quote')}
                      className={`block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 ${activeMainTab === 'new-quote' ? 'text-[#00B69B]' : 'text-gray-700'}`}
                    >
                      New Quote
                    </button>
                    <button
                      onClick={() => {
                        setActiveMainTab('saved-quotes');
                        fetchSavedQuotes();
                      }}
                      className={`block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 ${activeMainTab === 'saved-quotes' ? 'text-[#00B69B]' : 'text-gray-700'}`}
                    >
                      Saved Quotes
                    </button>
                  </div>
                </div>
                <button
                  onClick={() => setActiveMainTab('settings')}
                  className={`inline-flex items-center px-4 pt-1 border-b-2 text-sm font-medium ${activeMainTab === 'settings'
                    ? 'border-[#00B69B] text-[#00B69B]'
                    : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'}`}
                >
                  Settings
                </button>
                <button
                  onClick={() => setActiveMainTab('help')}
                  className={`inline-flex items-center px-4 pt-1 border-b-2 text-sm font-medium ${activeMainTab === 'help'
                    ? 'border-[#00B69B] text-[#00B69B]'
                    : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'}`}
                >
                  Help
                </button>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <select
                value={selectedCurrency}
                onChange={(e) => setSelectedCurrency(e.target.value)}
                className="ml-3 px-3 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-[#00B69B] focus:border-[#00B69B] sm:text-sm"
              >
                {Object.keys(CURRENCIES).map(currency => (
                  <option key={currency} value={currency}>{currency}</option>
                ))}
              </select>
              {user ? (
                <>
                  <span className="text-gray-700">{user.email || user.user_metadata?.name}</span>
                  <button onClick={handleLogout} className="text-sm text-[#00B69B] hover:text-[#00a089]">Logout</button>
                </>
              ) : (
                <>
                  <Link to="/login" className="text-sm text-gray-700 hover:text-gray-900">Login</Link>
                  <Link to="/signup" className="ml-4 text-sm text-[#00B69B] hover:text-[#00a089]">Sign Up</Link>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeMainTab === 'saved-quotes' ? (
          <SavedQuotesTabView />
        ) : (
          <>
            <div className="mb-8 flex flex-col items-center">
              <h1 className="text-3xl font-bold text-[#00B69B] mb-2">TripXplo Package Quote</h1>
              <p className="text-gray-600">Generate a custom travel package quote</p>
            </div>

            {/* Saved Quotes Modal */}
            {showQuoteModal && <SavedQuotesModal />}

        {/* Tabs */}
        <div className="mb-8">
          <div className="flex border-b border-gray-300 mb-6">
            <button
              onClick={() => setActiveTab('general')}
              className={`px-6 py-3 font-medium text-sm rounded-t-lg ${
                activeTab === 'general'
                  ? 'bg-white border-t border-l border-r border-gray-300 text-[#00B69B]'
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <div className="flex items-center gap-2">
                <Calculator className="w-4 h-4" />
                Quote Generator
              </div>
            </button>
            <button
              onClick={() => setActiveTab('family')}
              className={`px-6 py-3 font-medium text-sm rounded-t-lg ${
                activeTab === 'family'
                  ? 'bg-white border-t border-l border-r border-gray-300 text-[#00B69B]'
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <div className="flex items-center gap-2">
                <Users className="w-4 h-4" />
                Family Type
              </div>
            </button>
            <button
              onClick={() => setActiveTab('quote-mapping')}
              className={`px-6 py-3 font-medium text-sm rounded-t-lg ${
                activeTab === 'quote-mapping'
                  ? 'bg-white border-t border-l border-r border-gray-300 text-[#00B69B]'
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <div className="flex items-center gap-2">
                <MapPin className="w-4 h-4" />
                Quote Mapping
              </div>
            </button>
            <button
              onClick={() => setActiveTab('emi')}
              className={`px-6 py-3 font-medium text-sm rounded-t-lg ${
                activeTab === 'emi'
                  ? 'bg-white border-t border-l border-r border-gray-300 text-[#00B69B]'
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <div className="flex items-center gap-2">
                <Calculator className="w-4 h-4" />
                EMI Calculator
              </div>
            </button>
            <button
              onClick={() => setActiveTab('packageOptions')}
              className={`px-6 py-3 font-medium text-sm rounded-t-lg ${
                activeTab === 'packageOptions'
                  ? 'bg-white border-t border-l border-r border-gray-300 text-[#00B69B]'
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <div className="flex items-center gap-2">
                <Calculator className="w-4 h-4" />
                Package Options
              </div>
            </button>
          </div>
        </div>

        {/* Tab Content Container */}
        <div className={`
          mt-6 bg-white rounded-lg
          ${activeTab !== 'general' ? 'p-6 border border-gray-200 shadow-sm' : ''}
        `}>
          {activeTab === 'general' && (
            <div>
              <div className="flex gap-4 mb-6">
                <button
                  onClick={resetForm}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 flex items-center gap-2 transition-all duration-200 border border-gray-300"
                >
                  <RotateCcw className="w-4 h-4" />
                  Reset Form
                </button>
                <button
                  onClick={() => saveQuote(true)}
                  className="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 flex items-center gap-2"
                >
                  <Save className="w-4 h-4" />
                  {currentQuoteId ? 'Update' : 'Save Draft'}
                </button>
                <button
                  onClick={() => saveQuote(false)}
                  className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 flex items-center gap-2"
                >
                  <Save className="w-4 h-4" />
                  Save Final
                </button>
                <div className="relative" ref={recentQuotesDropdownRef}>
                  <button
                    onClick={() => setShowRecentQuotesDropdown((prev: boolean) => !prev)}
                    className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 flex items-center gap-2"
                  >
                    <Save className="w-4 h-4" />
                    Recent Quotes
                  </button>
                  {showRecentQuotesDropdown && (
                    <div className="absolute z-10 mt-2 w-72 bg-white border border-gray-200 rounded-lg shadow-lg max-h-96 overflow-y-auto">
                      <div className="p-2 font-semibold text-gray-700 border-b">Last 10 Quotes</div>
                      {recentQuotes.length === 0 ? (
                        <div className="p-4 text-gray-500">No recent quotes found.<br/>[Debug: recentQuotes is empty]</div>
                      ) : (
                        <ul>
                          {recentQuotes.map((quote: any, idx: number) => (
                            <li key={quote.id || idx} className="flex items-center justify-between px-4 py-2 hover:bg-gray-100">
                              <div>
                                <div className="font-medium">{quote.package_name || 'Unnamed'}</div>
                                <div className="text-xs text-gray-500">{quote.customer_name} &bull; {quote.created_at ? new Date(quote.created_at).toLocaleDateString() : 'No Date'}</div>
                                <div className="text-xs text-gray-600">Total Cost: {formatPrice(quote.total_cost)}</div>
                              </div>
                              <button
                                onClick={() => {
                                  loadQuote(quote.id);
                                  setShowRecentQuotesDropdown(false);
                                }}
                                className="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200"
                              >
                                Load
                              </button>
                            </li>
                          ))}
                        </ul>
                      )}
                    </div>
                  )}
                </div>
                <button
                  onClick={() => setHotelRows([...hotelRows, {
                    hotelName: '',
                    roomType: 'Standard',
                    roomCapacity: 2,
                    price: 0,
                    mealPlan: 'MAP',
                    noOfRooms: 0,
                    stayNights: 0,
                    extraAdultCost: 0,
                    childrenCost: 0,
                    infantCost: 0,
                    gstType: '0',
                    tacPercentage: 0,
                    tacAmount: 0,
                    info: '',
                    stayPrice: 0,
                    gstAmount: 0,
                    currency: selectedCurrency
                  }])}
                  className="px-3 py-1.5 bg-[#00B69B] text-white rounded-md hover:bg-[#00a089] flex items-center gap-1.5 text-sm transition-all duration-200"
                >
                  <Plus className="w-4 h-4" />
                  Add Hotel
                </button>
              </div>

                  {/* Package Details Card */}
                  <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
                    <div className="p-6 border-b border-gray-200 bg-gray-50">
                      <h3 className="text-lg font-semibold text-gray-800">Package Details</h3>
                    </div>

                    <div className="p-6 space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div className="col-span-1 md:col-span-2">
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">Package ID</label>
                              <input
                                type="text"
                                value={packageId}
                                disabled
                                className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 text-gray-500"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">Package Name</label>
                              <input
                                type="text"
                                value={packageName}
                                onChange={(e) => setPackageName(e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#00B69B] focus:border-[#00B69B]"
                                placeholder="Enter package name"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">Customer Name</label>
                              <input
                                type="text"
                                value={customerName}
                                onChange={(e) => setCustomerName(e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#00B69B] focus:border-[#00B69B]"
                                placeholder="Enter customer name"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">
                                Customer Mobile
                                <span className="text-xs text-green-600 ml-1">(For WhatsApp & Quote Mapping)</span>
                              </label>
                              <input
                                type="tel"
                                value={recipientPhoneNumber}
                                onChange={(e) => setRecipientPhoneNumber(e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#00B69B] focus:border-[#00B69B]"
                                placeholder="Enter 10-digit mobile number"
                                maxLength={10}
                                pattern="[0-9]{10}"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">Customer Email</label>
                              <input
                                type="email"
                                value={recipientEmail}
                                onChange={(e) => setRecipientEmail(e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#00B69B] focus:border-[#00B69B]"
                                placeholder="Enter customer email"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">Package Tier</label>
                              <select
                                value={plan}
                                onChange={(e) => setPlan(e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#00B69B] focus:border-[#00B69B]"
                              >
                                <option value="">Select Package Tier</option>
                                {GENERAL_OPTIONS.plans.map(plan => (
                                  <option key={plan.value} value={plan.value}>
                                    {plan.label}
                                  </option>
                                ))}
                              </select>
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">Destination</label>
                              <DestinationAutocomplete
                                value={destination}
                                onChange={setDestination}
                                placeholder="Enter Destination"
                              />
                            </div>
                          </div>
                        </div>

                        <div>
                          <div className="bg-[#f8fcfb] border border-[#d7f3ef] rounded-lg p-4">
                            <h4 className="text-sm font-semibold text-gray-700 mb-3">Travel Group</h4>
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <label className="block text-sm font-medium text-gray-600 mb-1">Persons</label>
                                <NumberInput
                                  value={noOfPersons}
                                  onChange={setNoOfPersons}
                                  min={0}
                                  className="bg-white"
                                />
                              </div>
                              <div>
                                <label className="block text-sm font-medium text-gray-600 mb-1">Extra Adults</label>
                                <NumberInput
                                  value={extraAdults}
                                  onChange={setExtraAdults}
                                  min={0}
                                  className="bg-white"
                                />
                              </div>
                              <div>
                                <label className="block text-sm font-medium text-gray-600 mb-1">Children (2-11)</label>
                                <NumberInput
                                  value={children}
                                  onChange={setChildren}
                                  min={0}
                                  className="bg-white"
                                />
                              </div>
                              <div>
                                <label className="block text-sm font-medium text-gray-600 mb-1">Infants (&lt;2)</label>
                                <NumberInput
                                  value={infants}
                                  onChange={setInfants}
                                  min={0}
                                  className="bg-white"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

              {/* Trip Details Section - Moved here right after Package Details */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
                <div className="p-6 border-b border-gray-200 bg-gray-50">
                  <h3 className="text-lg font-semibold text-gray-800">Trip Details</h3>
                </div>
                <div className="p-6">
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Trip Duration</label>
                      <select
                        value={tripDuration}
                        onChange={(e) => setTripDuration(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#00B69B] focus:border-[#00B69B]"
                      >
                        <option value="">Select Duration</option>
                        {GENERAL_OPTIONS.durations.map(duration => (
                          <option key={duration} value={duration}>{duration}</option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Package Type</label>
                      <PackageTypeAutocomplete
                        value={packageType}
                        onChange={setPackageType}
                        placeholder="Select Package Type"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Trip Type</label>
                      <FamilyTypeAutocomplete
                        value={tripType}
                        onChange={setTripType}
                        placeholder="Enter Trip Type"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Quote Date</label>
                      <input
                        type="date"
                        value={quoteDate}
                        onChange={(e) => setQuoteDate(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#00B69B] focus:border-[#00B69B]"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Package Validity</label>
                      <input
                        type="date"
                        value={validityDate}
                        onChange={(e) => setValidityDate(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#00B69B] focus:border-[#00B69B]"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Season</label>
                      <select
                        value={season}
                        onChange={(e) => setSeason(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#00B69B] focus:border-[#00B69B]"
                      >
                        <option value="">Select Season</option>
                        {GENERAL_OPTIONS.seasons.map(season => (
                          <option key={season.value} value={season.value}>{season.label}</option>
                        ))}
                      </select>
                    </div>

                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-600 mb-1">
                        Quote Type
                      </label>
                      <select
                        value={quoteType}
                        onChange={(e) => setQuoteType(e.target.value)}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-[#00B69B] focus:border-[#00B69B]"
                      >
                        <option value="">Select Quote Type</option>
                        {GENERAL_OPTIONS.quoteTypes.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-600 mb-1">
                        Currency
                      </label>
                      <select
                        value={selectedCurrency}
                        onChange={(e) => setSelectedCurrency(e.target.value)}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-[#00B69B] focus:border-[#00B69B]"
                      >
                        {Object.entries(CURRENCIES).map(([code, { name, symbol }]) => (
                          <option key={code} value={code}>
                            {code} - {name} ({symbol})
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="flex flex-col gap-2">
                      <label className="text-sm font-medium text-gray-700">Cab Type</label>
                      <div className="relative">
                        <input
                          type="text"
                          value={cabType}
                          onChange={(e) => setCabType(e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                          placeholder="Select cab type"
                          list="cabTypes"
                        />
                        <datalist id="cabTypes">
                          {CAB_TYPE_OPTIONS.map((type) => (
                            <option key={type} value={type} />
                          ))}
                        </datalist>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Hotel table section */}
                  <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
                    <div className="p-6 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
                      <h3 className="text-lg font-semibold text-gray-800">Hotel Details</h3>
                      <button
                        onClick={() => setHotelRows([...hotelRows, {
                          hotelName: '',
                          roomType: 'Standard',
                          roomCapacity: 2,
                          price: 0,
                          mealPlan: 'MAP',
                          noOfRooms: 0,
                          stayNights: 0,
                          extraAdultCost: 0,
                          childrenCost: 0,
                          infantCost: 0,
                          gstType: '0',
                          tacPercentage: 0,
                          tacAmount: 0,
                          info: '',
                          stayPrice: 0,
                          gstAmount: 0,
                          currency: selectedCurrency
                        }])}
                        className="px-3 py-1.5 bg-[#00B69B] text-white rounded-md hover:bg-[#00a089] flex items-center gap-1.5 text-sm transition-all duration-200"
                      >
                        <Plus className="w-4 h-4" />
                        Add Hotel
                      </button>
                    </div>
                    <div className="p-6 overflow-x-auto">
                      <table className="w-full border-collapse">
                        <thead>
                          <tr className="bg-gray-50 text-sm">
                            <th className="border border-gray-200 px-4 py-2 text-left">Actions</th>
                            <th className="border border-gray-200 px-4 py-2 text-left">Hotel Name</th>
                            <th className="border border-gray-200 px-4 py-2 text-left">Room Type</th>
                            <th className="border border-gray-200 px-4 py-2 text-left">Room Capacity</th>
                            <th className="border border-gray-200 px-4 py-2 text-left">Price</th>
                            <th className="border border-gray-200 px-4 py-2 text-left">Meal Plan</th>
                            <th className="border border-gray-200 px-4 py-2 text-left">Rooms</th>
                            <th className="border border-gray-200 px-4 py-2 text-left">Nights</th>
                            <th className="border border-gray-200 px-4 py-2 text-left">Extra Adult Cost</th>
                            <th className="border border-gray-200 px-4 py-2 text-left">Child Cost</th>
                            <th className="border border-gray-200 px-4 py-2 text-left">Infant Cost</th>
                            <th className="border border-gray-200 px-4 py-2 text-left">GST Type</th>
                            <th className="border border-gray-200 px-4 py-2 text-left">GST Amount</th>
                            <th className="border border-gray-200 px-4 py-2 text-left">TAC %</th>
                            <th className="border border-gray-200 px-4 py-2 text-left">TAC Amount</th>
                            <th className="border border-gray-200 px-4 py-2 text-left">Info</th>
                            <th className="border border-gray-200 px-4 py-2 text-left">Stay Price</th>
                          </tr>
                        </thead>
                        <tbody>
                          {hotelRows.map((row, index) => (
                            <tr key={index} className="hover:bg-gray-50">
                              <td className="border border-gray-200 px-2 py-2">
                                <button
                                  onClick={() => deleteHotelRow(index)}
                                  className="p-1.5 bg-red-50 text-red-600 hover:bg-red-100 rounded-md transition-colors"
                                  title="Delete row"
                                >
                                  <Trash2 className="w-4 h-4" />
                                </button>
                              </td>
                              <td className="border border-gray-200 px-2 py-2 min-w-[220px]">
                                <HotelAutocomplete
                                  value={row.hotelName}
                                  onChange={(value) => updateHotelRow(index, 'hotelName', value)}
                                  placeholder="Enter hotel name"
                                />
                              </td>
                              <td className="border border-gray-200 px-2 py-2 min-w-[120px]">
                                <RoomTypeAutocomplete
                                  value={row.roomType}
                                  onChange={(value) => updateHotelRow(index, 'roomType', value)}
                                  placeholder="Enter room type"
                                />
                              </td>
                              <td className="border border-gray-200 px-2 py-2 min-w-[80px]">
                                <input
                                  type="number"
                                  min={2}
                                  value={row.roomCapacity}
                                  onChange={e => updateHotelRow(index, 'roomCapacity', Number(e.target.value))}
                                  className="w-full border border-gray-300 rounded-md py-1 px-2 text-sm"
                                  placeholder="2"
                                />
                              </td>
                              <td className="border border-gray-200 px-2 py-2 min-w-[100px]">
                                <div className="flex items-center gap-1">
                                <NumberInput
                                  value={row.price}
                                  onChange={(value) => updateHotelRow(index, 'price', value)}
                                  isPrice
                                    className="text-right flex-grow min-w-[80px]"
                                  />
                                  <select
                                    value={row.currency || selectedCurrency}
                                    onChange={(e) => updateHotelRow(index, 'currency', e.target.value)}
                                    className="px-1 py-1 border border-gray-300 rounded-md text-xs focus:ring-2 focus:ring-[#00B69B] focus:border-[#00B69B] w-[60px]"
                                  >
                                    {Object.keys(CURRENCIES).map((code) => (
                                      <option key={code} value={code}>
                                        {code}
                                      </option>
                                    ))}
                                  </select>
                                </div>
                              </td>
                              <td className="border border-gray-200 px-2 py-2 min-w-[100px]">
                                <select
                                  value={row.mealPlan}
                                  onChange={(e) => updateHotelRow(index, 'mealPlan', e.target.value as typeof MEAL_PLAN_OPTIONS[number])}
                                  className="w-full px-2 py-1.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#00B69B] focus:border-[#00B69B]"
                                >
                                  {MEAL_PLAN_OPTIONS.map(option => (
                                    <option key={option} value={option}>
                                      {option}
                                    </option>
                                  ))}
                                </select>
                              </td>
                              <td className="border border-gray-200 px-2 py-2 min-w-[80px]">
                                <NumberInput
                                  value={row.noOfRooms}
                                  onChange={(value) => updateHotelRow(index, 'noOfRooms', value)}
                                  min={0}
                                />
                              </td>
                              <td className="border border-gray-200 px-2 py-2 min-w-[80px]">
                                <NumberInput
                                  value={row.stayNights}
                                  onChange={(value) => updateHotelRow(index, 'stayNights', value)}
                                  min={0}
                                />
                              </td>
                              <td className="border border-gray-200 px-2 py-2 min-w-[100px]">
                                <NumberInput
                                  value={row.extraAdultCost}
                                  onChange={(value) => updateHotelRow(index, 'extraAdultCost', value)}
                                  isPrice
                                  className="text-right"
                                />
                                <div className="text-xs text-gray-500 mt-1">
                                  Subtotal: {formatPrice(row.extraAdultCost * extraAdults * row.stayNights, row.currency || selectedCurrency)}
                                </div>
                              </td>
                              <td className="border border-gray-200 px-2 py-2 min-w-[100px]">
                                <NumberInput
                                  value={row.childrenCost}
                                  onChange={(value) => updateHotelRow(index, 'childrenCost', value)}
                                  isPrice
                                  className="text-right"
                                />
                              </td>
                              <td className="border border-gray-200 px-2 py-2 min-w-[100px]">
                                <NumberInput
                                  value={row.infantCost}
                                  onChange={(value) => updateHotelRow(index, 'infantCost', value)}
                                  isPrice
                                  className="text-right"
                                />
                              </td>
                              <td className="border border-gray-200 px-2 py-2 min-w-[100px]">
                                <select
                                  value={row.gstType}
                                  onChange={(e) => updateHotelRow(index, 'gstType', e.target.value as '0' | '12' | '18' | 'NET' | 'EXC')}
                                  className="w-full px-2 py-1.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#00B69B] focus:border-[#00B69B]"
                                >
                                  {GST_OPTIONS.map(option => (
                                    <option key={option.value} value={option.value}>
                                      {option.label}
                                    </option>
                                  ))}
                                </select>
                              </td>
                              <td className="border border-gray-200 px-2 py-2 min-w-[100px]">
                                <div className="text-right px-2 py-1.5 bg-gray-50 rounded">
                                  {formatPrice(row.gstAmount)}
                                </div>
                              </td>
                              <td className="border border-gray-200 px-2 py-2 min-w-[80px]">
                                <select
                                  value={row.tacPercentage}
                                  onChange={(e) => updateHotelRow(index, 'tacPercentage', Number(e.target.value))}
                                  className="w-full px-2 py-1.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#00B69B] focus:border-[#00B69B]"
                                >
                                  {TAC_OPTIONS.map(option => (
                                    <option key={option.value} value={option.value}>
                                      {option.label}
                                    </option>
                                  ))}
                                </select>
                              </td>
                              <td className="border border-gray-200 px-2 py-2 min-w-[100px]">
                                <div className="text-right px-2 py-1.5 bg-gray-50 rounded">
                                  {formatPrice(row.tacAmount)}
                                </div>
                              </td>
                              <td className="border border-gray-200 px-2 py-2 min-w-[150px]">
                                <input
                                  type="text"
                                  value={row.info}
                                  onChange={(e) => updateHotelRow(index, 'info', e.target.value)}
                                  className="w-full px-2 py-1.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#00B69B] focus:border-[#00B69B]"
                                  placeholder="Additional info"
                                />
                              </td>
                              <td className="border border-gray-200 px-2 py-2 min-w-[120px]">
                                <div className="font-semibold text-right px-2 py-1.5 bg-[#f8fcfb] rounded text-[#00B69B] border border-[#d7f3ef]">
                                  {formatPrice(row.stayPrice)}
                                </div>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>

                  {/* Moved Stay Summary section */}
                  <div className="mb-8 bg-white rounded-lg shadow-sm border border-gray-200">
                    <div
                      className="p-6 border-b border-gray-200 flex justify-between items-center cursor-pointer hover:bg-gray-50"
                      onClick={() => setIsStaySummaryOpen(!isStaySummaryOpen)}
                    >
                      <h3 className="text-lg font-semibold text-gray-800">Stay Summary</h3>
                      {isStaySummaryOpen ? (
                        <ChevronUp className="w-5 h-5 text-gray-500" />
                      ) : (
                        <ChevronDown className="w-5 h-5 text-gray-500" />
                      )}
                    </div>

                    {isStaySummaryOpen && (
                      <div className="p-6">
                        <div className="grid grid-cols-3 gap-6">
                          <div className="space-y-3">
                            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-md">
                              <span className="font-medium">Total Room Charges:</span>
                              <span className="font-bold text-blue-600">
                                {formatPrice(hotelRows.reduce((sum, row) =>
                                  sum + (row.price * row.noOfRooms * row.stayNights), 0))}
                              </span>
                            </div>
                            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-md">
                              <span className="font-medium">Total Extra Adult Cost:</span>
                              <span className="font-bold text-blue-600">
                                {formatPrice(calculateTotals().totalExtraAdultCost)}
                              </span>
                            </div>
                          </div>
                          <div className="space-y-3">
                            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-md">
                              <span className="font-medium">Total Children Cost:</span>
                              <span className="font-bold text-blue-600">
                                {formatPrice(calculateTotals().totalChildrenCost)}
                              </span>
                            </div>
                            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-md">
                              <span className="font-medium">Total Infant Cost:</span>
                              <span className="font-bold text-blue-600">
                                {formatPrice(calculateTotals().totalInfantCost)}
                              </span>
                            </div>
                          </div>
                          <div className="space-y-3">
                            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-md">
                              <span className="font-medium">Total GST:</span>
                              <span className="font-bold text-blue-600">
                                {formatPrice(hotelRows.reduce((sum, row) => sum + row.gstAmount, 0))}
                              </span>
                            </div>
                            <div className="flex justify-between items-center p-3 bg-blue-50 rounded-md">
                              <span className="font-semibold">Total Stay Price:</span>
                              <span className="font-bold text-xl text-blue-600"></span>
                                {formatPrice(calculateTotals().totalHotelCost)}
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Grid section with Other Costs and Total Calculation */}
              <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
                    {/* Other Costs Section */}
                <div className="lg:col-span-4">
                      <div className="h-full bg-white rounded-lg shadow-sm">
                        <div className="p-6 border-b border-gray-200">
                          <h3 className="text-lg font-semibold">Other Costs</h3>
                        </div>
                        <div className="p-4">
                          <table className="w-full border-collapse">
                            <thead>
                              <tr className="bg-gray-100">
                                <th className="border px-4 py-2 text-left w-[60%]">Type</th>
                                <th className="border px-4 py-2 text-left w-[40%]">Amount</th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr className="bg-gray-50">
                                <td className="border px-4 py-2 font-medium text-blue-600" colSpan={2}>Basic Costs</td>
                              </tr>
                              {Object.entries(costs.basicCosts).map(([key, value]) => (
                                <tr key={key} className="hover:bg-gray-50">
                                  <td className="border px-4 py-2 font-medium">
                                    {key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1')}
                                  </td>
                                  <td className="border px-4 py-2">
                                    <div className="flex justify-end">
                                      <NumberInput
                                        value={value}
                                        onChange={(newValue) => setCosts({
                                          ...costs,
                                          basicCosts: { ...costs.basicCosts, [key]: newValue }
                                        })}
                                        min={0}
                                        isPrice={true}
                                        className="w-32 text-right pr-2"
                                      />
                                    </div>
                                  </td>
                                </tr>
                              ))}
                              {/* Similar pattern for Add-On Costs and Optional Costs sections */}
                              <tr className="bg-gray-50">
                                <td className="border px-4 py-2 font-medium text-blue-600" colSpan={2}>Add-On Costs</td>
                              </tr>
                              {Object.entries(costs.addOnCosts).map(([key, value]) => (
                                <tr key={key} className="hover:bg-gray-50">
                                  <td className="border px-4 py-2 font-medium">
                                    {key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1')}
                                  </td>
                                  <td className="border px-4 py-2">
                                    <div className="flex justify-end">
                                      <NumberInput
                                        value={value}
                                        onChange={(newValue) => setCosts({
                                          ...costs,
                                          addOnCosts: { ...costs.addOnCosts, [key]: newValue }
                                        })}
                                        min={0}
                                        isPrice={true}
                                        className="w-32 text-right pr-2"
                                      />
                                    </div>
                                  </td>
                                </tr>
                              ))}
                              <tr className="bg-gray-50">
                                <td className="border px-4 py-2 font-medium text-blue-600" colSpan={2}>Optional Costs</td>
                              </tr>
                              {Object.entries(costs.optionalCosts).map(([key, value]) => (
                                <tr key={key} className="hover:bg-gray-50">
                                  <td className="border px-4 py-2 font-medium">
                                    {key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1')}
                                  </td>
                                  <td className="border px-4 py-2">
                                    <div className="flex justify-end">
                                      <NumberInput
                                        value={value}
                                        onChange={(newValue) => setCosts({
                                          ...costs,
                                          optionalCosts: { ...costs.optionalCosts, [key]: newValue }
                                        })}
                                        min={0}
                                        isPrice={true}
                                        className="w-32 text-right pr-2"
                                      />
                                    </div>
                                  </td>
                                </tr>
                              ))}
                              <tr className="bg-gray-50">
                                <td className="border px-4 py-2 font-medium text-blue-600" colSpan={2}>Commission</td>
                              </tr>
                              <tr className="hover:bg-gray-50">
                                <td className="border px-4 py-2 font-medium">Commission Rate</td>
                                <td className="border px-4 py-2">
                                  <div className="flex justify-end items-center gap-2">
                                    <select
                                      value={commission}
                                      onChange={(e) => setCommission(e.target.value as CommissionValue)}
                                      className="w-32 px-2 py-1.5 border border-gray-300 rounded-md"
                                    >
                                      {COMMISSION_OPTIONS.map(option => (
                                        <option key={option.value} value={option.value}>
                                          {option.label}
                                        </option>
                                      ))}
                                    </select>
                                    {commission === 'custom' && (
                                      <div className="flex items-center gap-1">
                                        <NumberInput
                                          value={customCommission}
                                          onChange={setCustomCommission}
                                          min={0}
                                          max={100}
                                          className="w-20 text-right"
                                        />
                                        <span className="text-gray-500">%</span>
                                      </div>
                                    )}
                                  </div>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                    {/* Total Calculation Section */}
                <div className="lg:col-span-8">
                      <div className="h-full bg-white rounded-lg shadow-sm border border-gray-200">
                        <div className="p-6 border-b border-gray-200 bg-gray-50">
                          <h3 className="text-lg font-semibold text-gray-800">Total Calculation</h3>
                        </div>
                        <div className="p-8">
                          <div className="space-y-4 divide-y divide-gray-100">
                            {Object.entries(calculateTotals())
                              .filter(([key]) => !['totalExtraAdultCost', 'totalChildrenCost', 'totalInfantCost'].includes(key))
                              .map(([key, value], _index) => (
                                <div
                                  key={key}
                                  className={`flex justify-between items-center py-3 ${
                                    key === 'grandTotal' || key === 'perPersonCost'
                                      ? 'bg-blue-50 px-4 rounded-lg'
                                      : ''
                                  }`}
                                >
                                  <span className="text-base font-bold text-gray-800">
                                    {key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1')}
                                  </span>
                                  <span className={`font-bold ${
                                    key === 'grandTotal' || key === 'perPersonCost'
                                      ? 'text-2xl text-blue-600 bg-white px-4 py-2 rounded-md shadow-sm'
                                      : 'text-lg text-gray-700'
                                  }`}>
                                    {Array.isArray(value)
                                      ? formatPrice(value.reduce((sum, detail) => sum + detail.finalPrice, 0))
                                      : formatPrice(value)}
                                  </span>
                                </div>
                              ))}
                          </div>
                          <div className="mt-10 pt-6 border-t border-gray-200 space-y-4">
                            {/* Discount / Promo Selection */}
                            <div className="flex flex-col space-y-4 mb-4">
                              <h3 className="font-medium text-gray-700">Apply Discount</h3>

                              <div className="flex space-x-4 mb-2">
                                <label className="inline-flex items-center">
                                  <input
                                    type="radio"
                                    className="form-radio text-green-500"
                                    name="discountMode"
                                    checked={discountMode === 'manual'}
                                    onChange={() => setDiscountMode('manual')}
                                  />
                                  <span className="ml-2">Manual Discount</span>
                                </label>

                                <label className="inline-flex items-center">
                                  <input
                                    type="radio"
                                    className="form-radio text-green-500"
                                    name="discountMode"
                                    checked={discountMode === 'promo'}
                                    onChange={() => setDiscountMode('promo')}
                                  />
                                  <span className="ml-2">Promo Code</span>
                                </label>
                              </div>

                              {discountMode === 'manual' && (
                                <div className="flex items-center space-x-3">
                                  <select
                                    value={discountType}
                                    onChange={(e) => setDiscountType(e.target.value as 'percentage' | 'amount')}
                                    className="form-select block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
                                  >
                                    <option value="percentage">Percentage (%)</option>
                                    <option value="amount">Fixed Amount</option>
                                  </select>

                                  <NumberInput
                                    value={discountValue}
                                    onChange={setDiscountValue}
                                    min={0}
                                    max={discountType === 'percentage' ? 100 : undefined}
                                    className="w-24"
                                  />

                                  {discountType === 'percentage' ? (
                                    <span className="text-gray-500">%</span>
                                  ) : (
                                    <span className="text-gray-500">{CURRENCIES[selectedCurrency].symbol}</span>
                                  )}
                                </div>
                              )}

                              {discountMode === 'promo' && (
                                <div className="flex items-center space-x-3">
                                  <select
                                    value={selectedPromoCode}
                                    onChange={(e) => setSelectedPromoCode(e.target.value)}
                                    className="form-select block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
                                  >
                                    <option value="">Select a promo code</option>
                                    {promoCodes.map(promo => (
                                      <option key={promo.code} value={promo.code}>
                                        {promo.code} - {promo.description} ({promo.type === 'percentage' ? `${promo.value}%` : `${CURRENCIES[selectedCurrency].symbol}${promo.value}`})
                                      </option>
                                    ))}
                                  </select>
                                </div>
                              )}
                            </div>

                            {/* Display calculated discount */}
                            {calculateTotals().discount > 0 && (
                              <div className="flex justify-between text-sm">
                                <span className="font-medium">Discount:</span>
                                <span className="font-medium text-red-600">-{formatPrice(calculateTotals().discount)}</span>
                              </div>
                            )}

                            {/* Display original subtotal and discounted subtotal */}
                            <div className="flex justify-between text-sm">
                              <span className="font-medium">Original Subtotal:</span>
                              <span className="font-medium">{formatPrice(calculateTotals().initialSubtotal)}</span>
                            </div>

                            <div className="flex justify-between text-sm">
                              <span className="font-medium">Subtotal After Discount:</span>
                              <span className="font-medium">{formatPrice(calculateTotals().subtotal)}</span>
                            </div>

                            <div className="flex gap-4">
                              <button
                                onClick={handlePrintPreview}
                                disabled={isPdfGenerating}
                                className="flex-1 px-6 py-3 bg-[#00B69B] text-white rounded-lg hover:bg-[#009e88]
                                         flex items-center justify-center gap-2 font-semibold text-lg shadow-sm
                                         transition-all duration-200 hover:shadow-md disabled:bg-gray-400"
                              >
                                {isPdfGenerating ? (
                                  <>Processing...</>
                                ) : (
                                  <>
                                    <Download className="w-5 h-5" />
                                    Print Preview
                                  </>
                                )}
                              </button>
                            <button
                              onClick={handleGeneratePDF}
                              disabled={isPdfGenerating}
                                className="flex-1 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700
                                       flex items-center justify-center gap-2 font-semibold text-lg shadow-sm
                                       transition-all duration-200 hover:shadow-md disabled:bg-gray-400"
                            >
                              {isPdfGenerating ? (
                                <>Generating PDF...</>
                              ) : (
                                <>
                                  <Download className="w-5 h-5" />
                                  Generate PDF
                                </>
                              )}
                            </button>
                            </div>
                            <button
                              onClick={handleGenerateFamilyTypePrices}
                              disabled={isFamilyTypePricesGenerating}
                              className="w-full px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700
                                       flex items-center justify-center gap-2 font-semibold text-lg shadow-sm
                                       transition-all duration-200 hover:shadow-md disabled:bg-gray-400"
                            >
                              {isFamilyTypePricesGenerating ? (
                                <>Generating Prices...</>
                              ) : (
                                <>
                                  <Users className="w-5 h-5" />
                                  Generate Family Type Prices
                                </>
                              )}
                            </button>
                            <button
                              onClick={sendEmail}
                              disabled={isEmailSending || !recipientEmail}
                              className="w-full px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700
                                       flex items-center justify-center gap-2 font-semibold text-lg shadow-sm
                                       transition-all duration-200 hover:shadow-md disabled:bg-gray-400"
                            >
                              {isEmailSending ? (
                                <>Sending Email...</>
                              ) : (
                                <>
                                  <Mail className="w-5 h-5" />
                                  Send Email
                                </>
                              )}
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Communication Controls */}
                  <div className="mt-6 space-y-4">
                    {/* Email Input */}
                    <div className="flex gap-4 items-center">
                      <input
                        type="email"
                        value={recipientEmail}
                        onChange={(e) => setRecipientEmail(e.target.value)}
                        placeholder="Enter recipient email"
                        className="px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                      <button
                        onClick={sendEmail}
                        disabled={isEmailSending || !recipientEmail}
                        className={`px-4 py-2 ${
                          !recipientEmail || isEmailSending ? 'bg-gray-400' : 'bg-indigo-600 hover:bg-indigo-700'
                        } text-white rounded-md flex items-center gap-2 min-w-[120px] justify-center`}
                      >
                        {isEmailSending ? (
                          'Sending...'
                        ) : (
                          <>
                            <Mail className="w-4 h-4" />
                            Send Email
                          </>
                        )}
                      </button>
                    </div>

                    {/* WhatsApp Input */}
                    <div className="flex gap-4 items-center">
                      <input
                        type="text"
                        value={recipientPhoneNumber}
                        onChange={(e) => setRecipientPhoneNumber(e.target.value)}
                        placeholder="Enter recipient phone number"
                        className="px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                      <button
                        onClick={sendWhatsAppMessage}
                        disabled={isWhatsAppSending || !recipientPhoneNumber}
                        className={`px-4 py-2 ${
                          !recipientPhoneNumber || isWhatsAppSending ? 'bg-gray-400' : 'bg-green-600 hover:bg-green-700'
                        } text-white rounded-md flex items-center gap-2 min-w-[120px] justify-center`}
                      >
                        {isWhatsAppSending ? (
                          'Opening...'
                        ) : (
                          <>
                            <i className="fab fa-whatsapp"></i>
                            Send WhatsApp
                          </>
                        )}
                      </button>
                    </div>

                    {/* PDF Button */}
                    <div className="flex gap-4">
                      <button
                        onClick={handlePrintPreview}
                        className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center gap-2"
                      >
                        <Download className="w-4 h-4" />
                        Print Preview
                      </button>
                      <button
                        onClick={handleGeneratePDF}
                        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center gap-2"
                      >
                        <Download className="w-4 h-4" />
                        Download PDF
                      </button>
                    </div>
                  </div>

                  {/* Family Type Prices Results */}
                  {(familyTypePricesResults.length > 0 || familyTypePricesMessage) && (
                    <div className="mt-6 bg-white rounded-lg shadow p-6">
                      <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                        <Users className="w-5 h-5" />
                        Family Type Prices Results
                      </h3>
                      
                      {familyTypePricesMessage && (
                        <div className={`mb-4 p-3 rounded-md ${familyTypePricesResults.length > 0 ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'}`}>
                          {familyTypePricesMessage}
                        </div>
                      )}

                      {familyTypePricesResults.length > 0 && (
                        <div className="overflow-x-auto">
                          <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                              <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Family Type</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Members</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rooms</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vehicle</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Price</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Details</th>
                              </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                              {familyTypePricesResults.map((result, index) => (
                                <tr key={result.familyType.family_id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    {result.familyType.family_type}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {result.familyType.no_of_adults}A
                                    {result.familyType.no_of_children > 0 && ` + ${result.familyType.no_of_children}C`}
                                    {result.familyType.no_of_infants > 0 && ` + ${result.familyType.no_of_infants}I`}
                                    {result.familyType.no_of_child > 0 && ` + ${result.familyType.no_of_child}K`}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {result.breakdown.rooms} rooms
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {result.familyType.cab_type}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm font-semibold text-green-600">
                                    {formatPrice(result.calculatedPrice)}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <div className="text-xs">
                                      Hotel: {formatPrice(result.breakdown.hotelCost)}<br/>
                                      Vehicle: {formatPrice(result.breakdown.vehicleCost)}<br/>
                                      Others: {formatPrice(result.breakdown.additionalCosts)}
                                    </div>
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Add these components in the appropriate place in your form, perhaps in the second tab */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                      <div>
                      <ChecklistSelector
                        title="Package Inclusions"
                        items={inclusionItems}
                        onChange={setInclusionItems}
                        />
                      </div>
                      <div>
                      <ChecklistSelector
                        title="Package Exclusions"
                        items={exclusionItems}
                        onChange={setExclusionItems}
                        />
                      </div>
                      </div>
                      </div>
          )}

          {activeTab === 'family' && (
            <FamilyTypeTab 
              currentQuoteId={currentQuoteId}
              liveQuoteData={{
                packageName,
                customerName,
                destination,
                quoteDate,
                validityDate,
                noOfPersons,
                extraAdults,
                children,
                infants,
                hotelRows,
                costs,
                commission: typeof commission === 'number' ? commission : 5,
                discountValue: typeof discountValue === 'number' ? discountValue : 0,
                discountType
              }}
            />
          )}
          {(activeTab as string) === 'quote-mapping' && <QuoteMappingTab />}
          {(activeTab as string) === 'emi' && <EmiCalculator />}
          {/* Tab content for Package Options */}
          {activeTab === "packageOptions" && (
            <div className="mb-8">
              {/* First column: Package options */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="col-span-1">
                  <h3 className="text-lg font-semibold mb-4">Package Options</h3>
                  <div className="space-y-4">
                    {/* Existing options... */}
                      <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Package Type
                      </label>
                        <select
                          value={packageType}
                          onChange={(e) => setPackageType(e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#00B69B] focus:border-[#00B69B]"
                        >
                          {GENERAL_OPTIONS.packageTypes.map(type => (
                          <option key={type.value} value={type.value}>
                            {type.label}
                          </option>
                          ))}
                        </select>
                      </div>

                    {/* Inclusions */}
                    <ChecklistSelector
                      items={inclusionItems}
                      onChange={setInclusionItems}
                      title="Package Inclusions"
                    />

                    {/* Exclusions */}
                    <ChecklistSelector
                      items={exclusionItems}
                      onChange={setExclusionItems}
                      title="Package Exclusions"
                    />

                    {/* Payment Options */}
                    <ChecklistSelector
                      items={paymentOptionItems}
                      onChange={setPaymentOptionItems}
                      title="Payment Options"
                    />

                    {/* Terms and Conditions */}
                    <ChecklistSelector
                      items={termItems}
                      onChange={setTermItems}
                      title="Terms & Conditions"
                    />
                      </div>
                      </div>

                {/* Second column: Promo Codes */}
                <div className="col-span-1 md:col-span-2">
                  <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                    <h3 className="text-lg font-semibold mb-4">Manage Promo Codes</h3>

                    <div className="overflow-x-auto mb-4">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Code</th>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Description</th>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Value</th>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Valid Until</th>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {promoCodes.map((promo) => (
                            <tr key={promo.code}>
                              <td className="px-4 py-2 text-sm text-gray-900">{promo.code}</td>
                              <td className="px-4 py-2 text-sm text-gray-900">{promo.description}</td>
                              <td className="px-4 py-2 text-sm text-gray-900">
                                {promo.type === 'percentage' ? 'Percentage' : 'Fixed Amount'}
                              </td>
                              <td className="px-4 py-2 text-sm text-gray-900">
                                {promo.type === 'percentage' ? `${promo.value}%` : formatPrice(promo.value)}
                              </td>
                              <td className="px-4 py-2 text-sm text-gray-900">
                                {promo.validUntil ? promo.validUntil : 'No expiry'}
                              </td>
                              <td className="px-4 py-2 text-sm text-gray-900">
                                <button
                                  onClick={() => handleDeletePromoCode(promo.code)}
                                  className="text-red-600 hover:text-red-800"
                                >
                                  Delete
                                </button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>

                    {/* Add new promo code form */}
                    <div className="mt-4">
                      <h4 className="font-medium mb-2">Add New Promo Code</h4>
                      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <input
                          type="text"
                          placeholder="Code"
                          value={newPromoCode.code}
                          onChange={(e) => setNewPromoCode({...newPromoCode, code: e.target.value.toUpperCase()})}
                          className="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
                        />
                        <input
                          type="text"
                          placeholder="Description"
                          value={newPromoCode.description}
                          onChange={(e) => setNewPromoCode({...newPromoCode, description: e.target.value})}
                          className="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
                        />
                        <select
                          value={newPromoCode.type}
                          onChange={(e) => setNewPromoCode({...newPromoCode, type: e.target.value as 'percentage' | 'amount'})}
                          className="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
                        >
                          <option value="percentage">Percentage</option>
                          <option value="amount">Fixed Amount</option>
                        </select>
                        <input
                          type="number"
                          placeholder="Value"
                          value={newPromoCode.value || ''}
                          onChange={(e) => setNewPromoCode({...newPromoCode, value: Number(e.target.value)})}
                          className="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
                        />
                        <input
                          type="date"
                          placeholder="Valid Until"
                          value={newPromoCode.validUntil}
                          onChange={(e) => setNewPromoCode({...newPromoCode, validUntil: e.target.value})}
                          className="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
                        />
                      </div>
                      <button
                        onClick={handleAddPromoCode}
                        className="mt-4 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                      >
                        Add Promo Code
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
                       </div>

          </>
        )}

        {/* Copyright Footer - Only shown for new-quote tab */}
        {activeMainTab === 'new-quote' && (
          <div className="mt-10 py-4 text-center text-gray-500 text-sm border-t border-gray-200">
            <p>© TripXplo by Tripmilestone Tours Pvt Ltd</p>
          </div>
        )}
      </div>
    </div>
  );
}

export default App;
