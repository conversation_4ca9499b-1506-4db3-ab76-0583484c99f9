import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { fetchLeadsByUser, updateLeadStatus } from '../lib/leadService';
import { LEAD_STATUSES } from '../constants/leadStatus';
import { Lead } from '../components/LeadCard';
import { Spinner } from '../components/Spinner';
import { toast } from 'react-hot-toast';
import { supabase } from '../lib/supabaseClient';
import { Phone, ExternalLink, Search, Filter, RefreshCw, ChevronLeft, ChevronRight } from 'lucide-react';

const LeadsTable: React.FC = () => {
  const { user } = useAuth();
  const [leads, setLeads] = useState<Lead[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('ALL');
  const [packageTypeFilter, setPackageTypeFilter] = useState<string>('ALL');
  const [updatingLeadId, setUpdatingLeadId] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(50);
  const [userProfiles, setUserProfiles] = useState<{ [key: string]: string }>({});
  const [quotesData, setQuotesData] = useState<{ [key: string]: any[] }>({});
  const [showInsights, setShowInsights] = useState(false);

  useEffect(() => {
    if (user?.id) {
      fetchLeads();
    }
  }, [user?.id]);

  // Fetch user profiles after leads are loaded
  useEffect(() => {
    if (leads.length > 0) {
      fetchUserProfiles();
      fetchQuotesForCustomers();
    }
  }, [leads]);

  const fetchLeads = async () => {
    if (!user?.id) {
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await fetchLeadsByUser(user.id);
      if (result.success && result.data) {
        setLeads(result.data);
      } else {
        throw new Error(result.error || 'Failed to fetch leads');
      }
    } catch (error: any) {
      console.error('Error fetching leads:', error);
      setError(error.message || 'Failed to load leads');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchUserProfiles = async () => {
    try {
      console.log('Fetching user profiles...');
      
      // Get all profiles
      const { data: profilesData, error: profilesError } = await supabase
        .from('profiles')
        .select('id, full_name, email');
      
      console.log('Profiles data:', profilesData, 'Error:', profilesError);
      
      const profilesMap: { [key: string]: string } = {};
      
      // Add profiles data
      if (profilesData && !profilesError) {
        profilesData.forEach((profile: any) => {
          const displayName = profile.full_name || 
                             (profile.email ? profile.email.split('@')[0] : '') || 
                             'User';
          profilesMap[profile.id] = displayName;
        });
      }
      
      // If we have leads but no matching profiles, create fallback names
      if (leads.length > 0) {
        leads.forEach(lead => {
          if (lead.assigned_to && !profilesMap[lead.assigned_to]) {
            // Try to extract a name from the assigned_to ID or just use a generic name
            profilesMap[lead.assigned_to] = `User ${lead.assigned_to.substring(0, 8)}`;
          }
        });
      }
      
      console.log('Final profiles map:', profilesMap);
      setUserProfiles(profilesMap);
    } catch (error) {
      console.error('Error fetching user profiles:', error);
    }
  };

  const handleRefresh = async () => {
    toast.success('Refreshing leads...');
    await fetchLeads();
  };

  const handleStatusChange = async (leadId: string, newStatus: string) => {
    setUpdatingLeadId(leadId);
    
    try {
      const result = await updateLeadStatus(leadId, newStatus);
      if (result.success) {
        // Update local state
        setLeads(prevLeads => 
          prevLeads.map(lead => 
            lead.id === leadId ? { ...lead, status: newStatus } : lead
          )
        );
        toast.success('Lead status updated successfully');
      } else {
        throw new Error(result.error || 'Failed to update status');
      }
    } catch (error: any) {
      console.error('Error updating lead status:', error);
      toast.error(error.message || 'Failed to update lead status');
    } finally {
      setUpdatingLeadId(null);
    }
  };

  const handleWhatsAppClick = (phone: string, customerName: string, status?: string) => {
    if (!phone) {
      toast.error('No phone number available');
      return;
    }
    let cleanPhone = phone.trim().replace(/[-\s()]/g, '');
    let formattedPhone = '';
    let countryCode = '';
    if (cleanPhone.startsWith('+')) {
      const match = cleanPhone.match(/^\+(\d{1,3})(\d{6,})$/);
      if (match) {
        countryCode = match[1];
        formattedPhone = countryCode + match[2];
      } else {
        formattedPhone = cleanPhone.replace(/[^\d+]/g, '').replace('+', '');
      }
    } else if (/^\d{10}$/.test(cleanPhone)) {
      countryCode = '91';
      formattedPhone = countryCode + cleanPhone;
    } else {
      formattedPhone = cleanPhone;
    }
    if (!/^\d{11,15}$/.test(formattedPhone)) {
      toast.error('Invalid phone number for WhatsApp. Please check the number format.');
      return;
    }
    let message = '';
    switch ((status || '').toUpperCase()) {
      case 'FOLLOW-UP':
      case 'FOLLOW UP':
        message = `Hi ${customerName},\n\nJust following up regarding your trip inquiry. Do you have any update or questions?\n\n- TripXplo Team`;
        break;
      case 'QUOTE SENT':
        message = `Hi ${customerName},\n\nWe have sent you a quote for your trip. Please check and let us know if you have any questions!\n\n- TripXplo Team`;
        break;
      case 'BOOKED WITH US':
        message = `Hi ${customerName},\n\nThank you for booking your trip with us! We look forward to serving you.\n\n- TripXplo Team`;
        break;
      default:
        message = `Hi ${customerName},\n\nThank you for your interest in TripXplo. Let us know if you have any questions!\n\n- TripXplo Team`;
    }
    const encodedMsg = encodeURIComponent(message);
    window.open(`https://web.whatsapp.com/send?phone=${formattedPhone}&text=${encodedMsg}`, '_blank');
  };

  const filteredLeads = leads.filter(lead => {
    const matchesSearch = !searchTerm || 
      lead.customer_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      lead.destination?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      lead.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      lead.phone?.includes(searchTerm) ||
      lead.package_type?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      lead.departure_city?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'ALL' || lead.status === statusFilter;
    const matchesPackageType = packageTypeFilter === 'ALL' || lead.package_type === packageTypeFilter;
    
    return matchesSearch && matchesStatus && matchesPackageType;
  });

  // Check if lead is upcoming (within next 2 months)
  const isUpcomingLead = (travelDate?: string) => {
    if (!travelDate) return false;
    try {
      const travel = new Date(travelDate);
      const now = new Date();
      const twoMonthsFromNow = new Date(now.getFullYear(), now.getMonth() + 2, now.getDate());
      return travel >= now && travel <= twoMonthsFromNow;
    } catch {
      return false;
    }
  };

  // Pagination
  const totalPages = Math.ceil(filteredLeads.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedLeads = filteredLeads.slice(startIndex, endIndex);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page
  };

  const fetchQuotesForCustomers = async () => {
    try {
      console.log('Fetching quotes for customers...');
      const { data, error } = await supabase
        .from('quotes')
        .select('id, customer_name, email, phone, destination, total_cost, created_at')
        .order('created_at', { ascending: false });
      
      if (error) {
        console.error('Error fetching quotes:', error);
        return;
      }
      
      // Group quotes by customer (email or phone)
      const quotesMap: { [key: string]: any[] } = {};
      data?.forEach(quote => {
        const key = quote.email || quote.phone || quote.customer_name;
        if (!quotesMap[key]) {
          quotesMap[key] = [];
        }
        quotesMap[key].push(quote);
      });
      
      setQuotesData(quotesMap);
      console.log('Quotes grouped by customer:', quotesMap);
    } catch (error) {
      console.error('Error fetching quotes:', error);
    }
  };

  const getQuotesForLead = (lead: Lead) => {
    const customerKey = lead.email || lead.phone || lead.customer_name;
    return quotesData[customerKey] || [];
  };

  const getInsights = () => {
    const totalLeads = leads.length;
    const upcomingLeads = leads.filter(lead => isUpcomingLead(lead.travel_date)).length;
    const statusCounts = LEAD_STATUSES.reduce((acc, status) => {
      acc[status] = leads.filter(lead => lead.status === status).length;
      return acc;
    }, {} as { [key: string]: number });
    
    const themeCounts = ['Honeymoon', 'Couple', 'Family', 'Friends', 'Corporate', 'Adventure', 'Luxury', 'Budget', 'Custom'].reduce((acc, theme) => {
      acc[theme] = leads.filter(lead => lead.package_type === theme).length;
      return acc;
    }, {} as { [key: string]: number });

    return {
      totalLeads,
      upcomingLeads,
      statusCounts,
      themeCounts,
      conversionRate: totalLeads > 0 ? ((statusCounts['BOOKED WITH US'] || 0) / totalLeads * 100).toFixed(1) : '0'
    };
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    try {
      const date = new Date(dateString);
      const day = date.getDate().toString().padStart(2, '0');
      const month = date.toLocaleDateString('en-US', { month: 'short' });
      const year = date.getFullYear();
      return `${day}-${month}-${year}`;
    } catch {
      return 'N/A';
    }
  };

  const getStatusColor = (status: string) => {
    const colors: { [key: string]: string } = {
      'NEW LEAD': 'bg-blue-100 text-blue-800 border-blue-200',
      'CALL CUSTOMER': 'bg-purple-100 text-purple-800 border-purple-200',
      'CONTACTED': 'bg-green-100 text-green-800 border-green-200',
      'CALL NOT ANSWERED': 'bg-orange-100 text-orange-800 border-orange-200',
      'NO RESPONSE': 'bg-red-100 text-red-800 border-red-200',
      'QUOTE SENT': 'bg-cyan-100 text-cyan-800 border-cyan-200',
      'APPROVED': 'bg-emerald-100 text-emerald-800 border-emerald-200',
      'BOOKED WITH US': 'bg-green-100 text-green-800 border-green-200',
      'NOT INTERESTED': 'bg-gray-100 text-gray-800 border-gray-200'
    };
    return colors[status] || 'bg-gray-100 text-gray-800 border-gray-200';
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <Spinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">Error: {error}</div>
        <button
          onClick={fetchLeads}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Leads Table View</h1>
          <p className="text-gray-600 mt-1">Manage all your leads in a spreadsheet-like interface</p>
        </div>
        <button
          onClick={handleRefresh}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <RefreshCw className="w-4 h-4" />
          Refresh
        </button>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search leads..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <div className="relative">
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="ALL">All Statuses</option>
              {LEAD_STATUSES.map(status => (
                <option key={status} value={status}>{status}</option>
              ))}
            </select>
          </div>
          <div className="relative">
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <select
              value={packageTypeFilter}
              onChange={(e) => setPackageTypeFilter(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="ALL">All Themes</option>
              <option value="Honeymoon">Honeymoon</option>
              <option value="Couple">Couple</option>
              <option value="Family">Family</option>
              <option value="Friends">Friends</option>
              <option value="Corporate">Corporate</option>
              <option value="Adventure">Adventure</option>
              <option value="Luxury">Luxury</option>
              <option value="Budget">Budget</option>
              <option value="Custom">Custom</option>
            </select>
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Quick Stats</h3>
          <button
            onClick={() => setShowInsights(!showInsights)}
            className="text-sm text-blue-600 hover:text-blue-800 transition-colors"
          >
            {showInsights ? 'Hide Insights' : 'Show Insights'}
          </button>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{filteredLeads.length}</div>
            <div className="text-sm text-gray-500">Filtered Leads</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {filteredLeads.filter(lead => lead.status === 'BOOKED WITH US').length}
            </div>
            <div className="text-sm text-gray-500">Booked</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">
              {filteredLeads.filter(lead => ['NEW LEAD', 'CALL CUSTOMER', 'CONTACTED'].includes(lead.status)).length}
            </div>
            <div className="text-sm text-gray-500">Active</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">
              {filteredLeads.filter(lead => isUpcomingLead(lead.travel_date)).length}
            </div>
            <div className="text-sm text-gray-500">Upcoming (2M)</div>
          </div>
        </div>
        
        {showInsights && (
          <div className="mt-6 grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Status Distribution</h4>
              <div className="space-y-2">
                {Object.entries(getInsights().statusCounts).filter(([_, count]) => count > 0).map(([status, count]) => (
                  <div key={status} className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">{status}</span>
                    <span className="text-sm font-medium">{count}</span>
                  </div>
                ))}
              </div>
              <div className="mt-3 pt-3 border-t">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-gray-700">Conversion Rate</span>
                  <span className="text-sm font-bold text-green-600">{getInsights().conversionRate}%</span>
                </div>
              </div>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Theme Distribution</h4>
              <div className="space-y-2">
                {Object.entries(getInsights().themeCounts).filter(([_, count]) => count > 0).map(([theme, count]) => (
                  <div key={theme} className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">{theme}</span>
                    <span className="text-sm font-medium">{count}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-3 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">
                  <span className="hidden md:inline">Customer</span>
                  <span className="md:hidden">Customer</span>
                </th>
                <th className="px-3 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">
                  <span className="hidden md:inline">No of Nights</span>
                  <span className="md:hidden">Nights</span>
                </th>
                <th className="px-3 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">
                  <span className="hidden md:inline">Destination</span>
                  <span className="md:hidden">Dest</span>
                </th>
                <th className="px-3 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">
                  <span className="hidden md:inline">Mobile No</span>
                  <span className="md:hidden">Mobile</span>
                </th>
                <th className="px-3 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">
                  <span className="hidden md:inline">Lead Status</span>
                  <span className="md:hidden">Status</span>
                </th>
                <th className="px-3 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">
                  <span className="hidden md:inline">Travel Date</span>
                  <span className="md:hidden">Date</span>
                </th>
                <th className="px-3 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">
                  <span className="hidden md:inline">Relationship Manager</span>
                  <span className="md:hidden">Manager</span>
                </th>
                <th className="px-3 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">
                  <span className="hidden md:inline">Theme</span>
                  <span className="md:hidden">Theme</span>
                </th>
                <th className="px-3 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">
                  <span className="hidden md:inline">Departure City</span>
                  <span className="md:hidden">From</span>
                </th>
                <th className="px-3 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <span className="hidden md:inline">Quotes</span>
                  <span className="md:hidden">Quotes</span>
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {paginatedLeads.map((lead) => {
                const isUpcoming = isUpcomingLead(lead.travel_date);
                return (
                <tr key={lead.id} className={`hover:bg-gray-50 transition-colors ${
                  isUpcoming ? 'bg-gradient-to-r from-yellow-50 to-orange-50 border-l-4 border-orange-400' : ''
                }`}>
                  <td className="px-3 md:px-6 py-4 whitespace-nowrap border-r border-gray-200">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{lead.customer_name}</div>
                      <div className="text-xs md:text-sm text-gray-500 hidden md:block">{lead.email}</div>
                    </div>
                  </td>
                  <td className="px-3 md:px-6 py-4 whitespace-nowrap border-r border-gray-200">
                    <div className="text-sm text-gray-900">{lead.nights || 'N/A'}</div>
                    <div className="text-xs text-gray-500 hidden md:block">
                      {lead.adults || 0}A {lead.children || 0}C {lead.infants || 0}I
                    </div>
                  </td>
                  <td className="px-3 md:px-6 py-4 whitespace-nowrap border-r border-gray-200">
                    <div className="text-sm text-gray-900">{lead.destination || 'N/A'}</div>
                  </td>
                  <td className="px-3 md:px-6 py-4 whitespace-nowrap border-r border-gray-200">
                    {lead.phone ? (
                      <div className="flex flex-col md:flex-row items-start md:items-center gap-1 md:gap-2">
                        <span className="text-sm text-gray-900">{lead.phone}</span>
                        <button
                          onClick={() => handleWhatsAppClick(lead.phone!, lead.customer_name, lead.status)}
                          className="inline-flex items-center gap-1 px-2 py-1 text-xs bg-green-100 text-green-700 rounded-md hover:bg-green-200 transition-colors"
                          title="Open WhatsApp Chat"
                        >
                          <Phone className="w-3 h-3" />
                          <span className="hidden md:inline">WhatsApp</span>
                          <ExternalLink className="w-3 h-3" />
                        </button>
                      </div>
                    ) : (
                      <span className="text-sm text-gray-400">No phone</span>
                    )}
                  </td>
                  <td className="px-3 md:px-6 py-4 whitespace-nowrap border-r border-gray-200">
                    <select
                      value={lead.status}
                      onChange={(e) => handleStatusChange(lead.id, e.target.value)}
                      disabled={updatingLeadId === lead.id}
                      className={`text-xs md:text-sm border rounded-md px-1 md:px-2 py-1 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${getStatusColor(lead.status)} ${
                        updatingLeadId === lead.id ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
                      }`}
                    >
                      {LEAD_STATUSES.map(status => (
                        <option key={status} value={status}>{status}</option>
                      ))}
                    </select>
                  </td>
                  <td className="px-3 md:px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r border-gray-200">
                    {formatDate(lead.travel_date)}
                  </td>
                  <td className="px-3 md:px-6 py-4 whitespace-nowrap border-r border-gray-200">
                    <div className="text-sm text-gray-900">
                      {lead.assigned_to ? userProfiles[lead.assigned_to] || 'Unknown User' : 'Unassigned'}
                    </div>
                  </td>
                  <td className="px-3 md:px-6 py-4 whitespace-nowrap border-r border-gray-200">
                    <div className="text-sm text-gray-900">{lead.package_type || 'N/A'}</div>
                  </td>
                  <td className="px-3 md:px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-r border-gray-200">
                    {lead.departure_city || 'N/A'}
                  </td>
                  <td className="px-3 md:px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {(() => {
                      const customerQuotes = getQuotesForLead(lead);
                      return customerQuotes.length > 0 ? (
                        <div className="flex items-center gap-2">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {customerQuotes.length} quote{customerQuotes.length !== 1 ? 's' : ''}
                          </span>
                          <span className="text-xs text-gray-500">
                            ₹{customerQuotes.reduce((sum, q) => sum + (q.total_cost || 0), 0).toLocaleString()}
                          </span>
                        </div>
                      ) : (
                        <span className="text-xs text-gray-400">No quotes</span>
                      );
                    })()}
                  </td>
                </tr>
              );
            })}
            </tbody>
          </table>
        </div>
        
        {filteredLeads.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-500 mb-2">No leads found</div>
            <div className="text-sm text-gray-400">
              {searchTerm || statusFilter !== 'ALL' || packageTypeFilter !== 'ALL'
                ? 'Try adjusting your filters' 
                : 'Create your first lead to get started'}
            </div>
          </div>
        )}
      </div>

      {/* Pagination Controls */}
      {filteredLeads.length > 0 && (
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <div className="flex flex-col lg:flex-row justify-between items-center gap-4">
            <div className="flex items-center gap-4">
              <div className="text-sm text-gray-600">
                Showing {startIndex + 1} to {Math.min(endIndex, filteredLeads.length)} of {filteredLeads.length} leads
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">Show:</span>
                <select
                  value={itemsPerPage}
                  onChange={(e) => handleItemsPerPageChange(Number(e.target.value))}
                  className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value={50}>50</option>
                  <option value={75}>75</option>
                  <option value={100}>100</option>
                  <option value={150}>150</option>
                  <option value={200}>200</option>
                  <option value={250}>250</option>
                  <option value={300}>300</option>
                </select>
                <span className="text-sm text-gray-600">per page</span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className={`p-2 rounded-lg border ${
                  currentPage === 1 
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}
              >
                <ChevronLeft className="w-4 h-4" />
              </button>
              
              <div className="flex items-center gap-1">
                {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                  let pageNum = i + 1;
                  if (totalPages > 5 && currentPage > 3) {
                    pageNum = Math.min(currentPage - 2 + i, totalPages);
                    if (currentPage > totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    }
                  }
                  
                  return (
                    <button
                      key={pageNum}
                      onClick={() => handlePageChange(pageNum)}
                      className={`px-3 py-2 rounded-lg border text-sm ${
                        currentPage === pageNum 
                          ? 'bg-blue-600 text-white' 
                          : 'bg-white text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}
              </div>
              
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages || totalPages === 0}
                className={`p-2 rounded-lg border ${
                  currentPage === totalPages || totalPages === 0
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}
              >
                <ChevronRight className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default LeadsTable; 