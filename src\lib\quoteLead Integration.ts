import { supabase } from './supabaseClient';
import { toast } from 'react-hot-toast';

export interface QuoteLeadSync {
  quote_id: string;
  customer_phone: string;
  customer_name: string;
  customer_email?: string;
  destination: string;
  total_cost: number;
}

/**
 * Automatically sync quote data with leads based on mobile number
 * This function is called after saving a quote with a mobile number
 */
export const syncQuoteWithLead = async (quoteData: QuoteLeadSync): Promise<void> => {
  try {
    const { customer_phone, customer_name, customer_email, destination, total_cost, quote_id } = quoteData;
    
    if (!customer_phone) {
      console.log('No mobile number provided, skipping lead sync');
      return;
    }

    // Clean mobile number for matching
    const cleanPhone = customer_phone.replace(/\D/g, '');
    
    // Find matching leads by mobile number
    const { data: existingLeads, error: searchError } = await supabase
      .from('leads')
      .select('*')
      .or(`phone.eq.${cleanPhone},phone.eq.+91${cleanPhone},phone.eq.91${cleanPhone}`)
      .order('created_at', { ascending: false });

    if (searchError) {
      console.error('Error searching for matching leads:', searchError);
      return;
    }

    if (existingLeads && existingLeads.length > 0) {
      // Update the most recent lead with quote information
      const leadToUpdate = existingLeads[0];
      
      const updateData: any = {
        status: 'QUOTE SENT',
        notes: `${leadToUpdate.notes || ''}\n\n🎯 Quote Generated:\n- Destination: ${destination}\n- Quote ID: ${quote_id}\n- Total Cost: ₹${total_cost.toLocaleString()}\n- Generated on: ${new Date().toLocaleString('en-IN')}`.trim(),
        updated_at: new Date().toISOString(),
      };

      // Update customer info if it's more complete in the quote
      if (customer_name && customer_name.trim().length > (leadToUpdate.customer_name?.length || 0)) {
        updateData.customer_name = customer_name;
      }
      
      if (customer_email && !leadToUpdate.email) {
        updateData.email = customer_email;
      }

      const { error: updateError } = await supabase
        .from('leads')
        .update(updateData)
        .eq('id', leadToUpdate.id);

      if (updateError) {
        console.error('Error updating lead:', updateError);
        toast.error('Failed to sync with lead');
      } else {
        console.log('Successfully synced quote with lead:', leadToUpdate.id);
        toast.success(`📋 Lead updated: ${customer_name} - ${destination}`);
      }
    } else {
      // No existing lead found, create a new one
      const newLeadData = {
        customer_name,
        email: customer_email || null,
        phone: cleanPhone,
        destination,
        status: 'QUOTE SENT',
        lead_source: 'quote_generator',
        priority: 'medium',
        notes: `🎯 Auto-created from Quote:\n- Quote ID: ${quote_id}\n- Total Cost: ₹${total_cost.toLocaleString()}\n- Generated on: ${new Date().toLocaleString('en-IN')}`,
        assigned_to: 'system', // You can modify this based on your user management
        created_at: new Date().toISOString(),
      };

      const { data: newLead, error: createError } = await supabase
        .from('leads')
        .insert(newLeadData)
        .select()
        .single();

      if (createError) {
        console.error('Error creating lead:', createError);
        toast.error('Failed to create lead from quote');
      } else {
        console.log('Successfully created new lead from quote:', newLead.id);
        toast.success(`📋 New lead created: ${customer_name} - ${destination}`);
      }
    }
  } catch (error) {
    console.error('Error in quote-lead sync:', error);
    toast.error('Failed to sync quote with leads');
  }
};

/**
 * Find all quotes for a specific mobile number
 * Used in the Kanban board to show quote history
 */
export const getQuotesForMobile = async (mobileNumber: string) => {
  try {
    const cleanPhone = mobileNumber.replace(/\D/g, '');
    
    const { data, error } = await supabase
      .rpc('find_quotes_by_mobile', { mobile_input: cleanPhone });

    if (error) throw error;
    
    return {
      success: true,
      data: data || [],
    };
  } catch (error) {
    console.error('Error fetching quotes for mobile:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      data: [],
    };
  }
};

/**
 * Update lead status from quote actions
 * Called when specific actions are taken on quotes
 */
export const updateLeadFromQuoteAction = async (
  customerPhone: string, 
  action: 'quote_sent' | 'quote_approved' | 'quote_revised' | 'booking_confirmed',
  additionalInfo?: string
): Promise<void> => {
  try {
    if (!customerPhone) return;

    const cleanPhone = customerPhone.replace(/\D/g, '');
    
    // Find matching lead
    const { data: leads, error: searchError } = await supabase
      .from('leads')
      .select('*')
      .or(`phone.eq.${cleanPhone},phone.eq.+91${cleanPhone},phone.eq.91${cleanPhone}`)
      .order('created_at', { ascending: false })
      .limit(1);

    if (searchError || !leads || leads.length === 0) {
      console.log('No matching lead found for quote action');
      return;
    }

    const lead = leads[0];
    let newStatus = lead.status;
    let notePrefix = '';

    switch (action) {
      case 'quote_sent':
        newStatus = 'QUOTE SENT';
        notePrefix = '📤 Quote sent via WhatsApp';
        break;
      case 'quote_approved':
        newStatus = 'APPROVED';
        notePrefix = '✅ Quote approved by customer';
        break;
      case 'quote_revised':
        newStatus = 'NEGOTIATION';
        notePrefix = '🔄 Quote revised based on feedback';
        break;
      case 'booking_confirmed':
        newStatus = 'BOOKED WITH US';
        notePrefix = '🎉 Booking confirmed';
        break;
    }

    const updateNote = `${notePrefix}${additionalInfo ? ` - ${additionalInfo}` : ''}\nTimestamp: ${new Date().toLocaleString('en-IN')}`;
    
    const { error: updateError } = await supabase
      .from('leads')
      .update({
        status: newStatus,
        notes: `${lead.notes || ''}\n\n${updateNote}`.trim(),
        updated_at: new Date().toISOString(),
      })
      .eq('id', lead.id);

    if (updateError) {
      console.error('Error updating lead from quote action:', updateError);
    } else {
      console.log(`Lead status updated to ${newStatus} for action: ${action}`);
      toast.success(`Lead updated: ${newStatus}`);
    }
  } catch (error) {
    console.error('Error in updateLeadFromQuoteAction:', error);
  }
};

/**
 * Check if a mobile number has existing quotes
 * Used to show quote history in lead details
 */
export const hasExistingQuotes = async (mobileNumber: string): Promise<boolean> => {
  try {
    const result = await getQuotesForMobile(mobileNumber);
    return result.success && result.data.length > 0;
  } catch (error) {
    console.error('Error checking existing quotes:', error);
    return false;
  }
}; 