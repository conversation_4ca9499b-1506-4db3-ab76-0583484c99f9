-- Add Customer Phone Field to Main Quotes Table
-- Run this script in your Quote Supabase Database

-- Add customer_phone field to quotes table if it doesn't exist
ALTER TABLE quotes 
ADD COLUMN IF NOT EXISTS customer_phone VARCHAR(20);

-- Add email field if it doesn't exist (for complete customer info)
ALTER TABLE quotes 
ADD COLUMN IF NOT EXISTS customer_email VARCHAR(255);

-- Create indexes for efficient mobile number lookups
CREATE INDEX IF NOT EXISTS idx_quotes_customer_phone ON quotes(customer_phone);
CREATE INDEX IF NOT EXISTS idx_quotes_customer_email ON quotes(customer_email);
CREATE INDEX IF NOT EXISTS idx_quotes_customer_name ON quotes(customer_name);

-- Create a composite index for customer lookup by phone and name
CREATE INDEX IF NOT EXISTS idx_quotes_customer_lookup ON quotes(customer_phone, customer_name);

-- Add comment for documentation
COMMENT ON COLUMN quotes.customer_phone IS 'Customer mobile number for WhatsApp integration and quote grouping';
COMMENT ON COLUMN quotes.customer_email IS 'Customer email for communication and identification';

-- Update any existing quotes that might have phone numbers in other fields
-- (This is a safe operation that won't overwrite existing data)
UPDATE quotes 
SET customer_phone = NULL 
WHERE customer_phone = '' OR customer_phone = ' ';

-- Create a view that groups quotes by customer phone for easy management
CREATE OR REPLACE VIEW customer_quote_summary AS
SELECT 
    customer_phone,
    customer_name,
    customer_email,
    COUNT(*) as total_quotes,
    SUM(total_cost) as total_value,
    MAX(created_at) as latest_quote_date,
    MIN(created_at) as first_quote_date,
    ARRAY_AGG(id ORDER BY created_at DESC) as quote_ids,
    ARRAY_AGG(destination ORDER BY created_at DESC) as destinations
FROM quotes 
WHERE customer_phone IS NOT NULL 
    AND customer_phone != ''
    AND customer_phone != ' '
GROUP BY customer_phone, customer_name, customer_email
ORDER BY latest_quote_date DESC;

-- Add a function to find quotes by mobile number (with or without country code)
CREATE OR REPLACE FUNCTION find_quotes_by_mobile(mobile_input TEXT)
RETURNS TABLE (
    quote_id UUID,
    customer_name TEXT,
    customer_phone TEXT,
    customer_email TEXT,
    destination TEXT,
    total_cost DECIMAL,
    created_at TIMESTAMP WITH TIME ZONE,
    package_name TEXT
) AS $$
BEGIN
    -- Clean the input mobile number (remove spaces, dashes, country codes)
    mobile_input := REGEXP_REPLACE(mobile_input, '[^0-9]', '', 'g');
    
    -- Handle +91 country code removal
    IF LENGTH(mobile_input) = 12 AND LEFT(mobile_input, 2) = '91' THEN
        mobile_input := RIGHT(mobile_input, 10);
    END IF;
    
    -- Search for quotes with matching mobile number
    RETURN QUERY
    SELECT 
        q.id,
        q.customer_name::TEXT,
        q.customer_phone::TEXT,
        q.customer_email::TEXT,
        q.destination::TEXT,
        q.total_cost,
        q.created_at,
        q.package_name::TEXT
    FROM quotes q
    WHERE REGEXP_REPLACE(COALESCE(q.customer_phone, ''), '[^0-9]', '', 'g') = mobile_input
        OR REGEXP_REPLACE(COALESCE(q.customer_phone, ''), '[^0-9]', '', 'g') = '91' || mobile_input
    ORDER BY q.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger function to auto-update customer info consistency
CREATE OR REPLACE FUNCTION sync_customer_info()
RETURNS TRIGGER AS $$
BEGIN
    -- When a quote is updated with phone number, sync with leads table if exists
    IF NEW.customer_phone IS NOT NULL AND NEW.customer_phone != '' THEN
        -- This will be used later when we integrate with leads
        -- For now, just ensure phone number is properly formatted
        NEW.customer_phone := REGEXP_REPLACE(NEW.customer_phone, '[^0-9]', '', 'g');
        
        -- Ensure it's a valid 10-digit Indian mobile number
        IF LENGTH(NEW.customer_phone) = 10 AND LEFT(NEW.customer_phone, 1) IN ('6', '7', '8', '9') THEN
            -- Valid mobile number, keep as is
        ELSIF LENGTH(NEW.customer_phone) = 12 AND LEFT(NEW.customer_phone, 2) = '91' THEN
            -- Remove country code
            NEW.customer_phone := RIGHT(NEW.customer_phone, 10);
        ELSE
            -- Invalid mobile number, set to NULL
            NEW.customer_phone := NULL;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS trigger_sync_customer_info ON quotes;
CREATE TRIGGER trigger_sync_customer_info
    BEFORE INSERT OR UPDATE ON quotes
    FOR EACH ROW
    EXECUTE FUNCTION sync_customer_info();

-- Grant necessary permissions
GRANT SELECT ON customer_quote_summary TO PUBLIC;
GRANT EXECUTE ON FUNCTION find_quotes_by_mobile(TEXT) TO PUBLIC;

-- Display success message
SELECT 'Customer phone field and mobile number mapping successfully added to quotes table!' as result; 